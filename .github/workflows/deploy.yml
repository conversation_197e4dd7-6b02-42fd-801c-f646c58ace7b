name: Deploy
on:
  push:

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest

    permissions:
      id-token: write # Needed for auth with Deno Deploy
      contents: read # Needed to clone the repository 

    steps:
      - name: Clone repository
        uses: actions/checkout@v4

      - name: Install Den<PERSON>
        uses: denoland/setup-deno@v2
        with:
          deno-version: v2.2.0

      - name: TypeScript Check
        run: "deno task ts-check"

      - name: Build step
        run: "deno task build"

      - name: Upload to Deno Deploy
        uses: denoland/deployctl@v1
        with:
          project: "rpgmeet"
          entrypoint: "./src/scripts/serve.ts"
          root: ""
      
      - name: Notify deployment
        env:
          TELEGRAM_TOKEN: ${{ secrets.TELEGRAM_TOKEN }}
          TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}
        run: |
          deno run --allow-all ./src/scripts/notify-deployment.ts 
