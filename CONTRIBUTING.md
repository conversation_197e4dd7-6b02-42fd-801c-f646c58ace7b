# The Pipeline

There are 2 fixed branches: `production` and `beta`. The pipeline follows 3
steps:

## 1. Feature development

Is a process where you create new features and add them to the `beta` branch.

1. create a named branch from the `production` branch following the scheme

```
[<impact>/]<type>/<content>
```

- **impact**: `patch` (default), `minor`, `major`; the CI will take this value
  into account to generate a new semver version (MAJOR.MINOR.PATCH);
- **type**: `feature`, `bug`, `enhancement`, `documentation`, `test`;
- **content**: the name of the feature or bug;

2. create a pull request to the `beta` branch;

When the code is merged, this will trigger the CI to:

- a new semver version is generated (MAJOR.MINOR.PATCH);
- a new version of the extension is built and uploaded to the Chrome Webstore
  (BETA extension).

## 2. Feature testing

Is a process where you test the new features available in the `beta` branch.

The code in the `beta` branch is automatically uploaded (see
[3. Release](#release)) to che Chrome Webstore (BETA extension).

This will be manually reviewed by the team and if it is approved, it should be
merged into the `production` branch.

## 3. Release

Is a process where you merge the `beta` branch into `production` and it results
in a new release, that is automatically uploaded to the Chrome Webstore
(PRODUCTION).

This merging triggers the following actions (through GitHub Actions):

1. the new version is added to the `manifest` object in the `package.json` file;
2. `production` code is built and zipped;
3. a new release is created (Github Releases);
4. the zip is uploaded to the Chrome Webstore (PRODUCTION extension)
