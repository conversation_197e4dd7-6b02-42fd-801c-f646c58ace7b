# RPG-MEET

Add RPG tools to your session: roll dice, draw tokens, add webcam overlays,
listen to the same audio in real time, and much more!

With this webapp, you can enhance your online RPG sessions with various tools and features.

Now available in English, French, Italian, Latvian, Spanish, Portuguese,
Urkainian.

## HAVE FUN TOGETHER THROWING DICE, DRAWING TOKENS AND MUCH MORE

You will be automatically connected to the other participants and be able to
throw dice from one of the game sets, the other participants will see in real
time what you have thrown.

Game sets available:

- Polyhedric Dice: d2(coin), d4, d6, d8, d10, d12. d20, d100
- Deck of 52 Cards
- Achtung! Cthulhu™
- Agon™
- Broken Compass™
- City Of Mist™
- Cthulhu Dark™
- Don't Rest Your Head™
- Fantasy World™
- Fate™
- Legend of the Five Rings™
- Not the End™
- Any PBTA - Powered By The Apocalypse™
- Six Bullets System™
- Spire™
- Valraven™
- Vampire: the Masquerade™
- Vampire: the Masquerade™ (V20)

## LISTEN TO THE SAME MUSIC

Share any browser's tab audio (YouTube, Spotify, SoundCloud...) or the screen
audio: the audio will be reproduced on the devices of all the
participants...simultaneously!

## WEBCAM OVERLAYS

Add a banner with title and subtitle, a table with free text and an avatar to
your stream. You can now apply filters (blur, brightness, contrast, grayscale,
invert, saturate, sepia)!

## INSTALLATION AND SETUP INSTRUCTIONS FOR DEVELOPERS

To set up the project locally, follow these steps:

1. Clone the repository:
   ```sh
   git clone https://github.com/marcomow/rpg-meet.git
   cd rpg-meet
   ```

2. Install the dependencies:
   ```sh
   deno task cache
   ```

3. Create a `.env` file based on the `.env.example` file and fill in the required environment variables.

4. Start the development server:
   ```sh
   deno task develop
   ```

## CONTRIBUTING GUIDELINES

We welcome contributions to the project! To contribute, follow these steps:

1. Fork the repository.
2. Create a new branch for your feature or bugfix.
3. Make your changes and commit them with clear and concise commit messages.
4. Push your changes to your forked repository.
5. Create a pull request to the `alpha` branch of the main repository.

Please ensure that your code follows the project's coding standards and includes appropriate tests.

## DEPENDENCIES AND REQUIREMENTS

The project has the following dependencies and requirements:

- Deno
- Various Deno modules and npm packages (see `deno.json` and `deno.lock` for details)

## CONTACT INFORMATION AND FURTHER DOCUMENTATION

For more information, please visit the [project's GitHub repository](https://github.com/marcomow/rpg-meet).

If you have any questions or need further assistance, feel free to open an issue on GitHub or contact the project <NAME_EMAIL>.
