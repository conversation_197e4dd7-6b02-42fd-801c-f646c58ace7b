{"compilerOptions": {"lib": ["deno.ns", "dom"]}, "unstable": ["kv"], "nodeModulesDir": "none", "imports": {"@gramio/wrappergram": "jsr:@gramio/wrappergram@^1.2.0", "@kinde-oss/kinde-auth-pkce-js": "npm:@kinde-oss/kinde-auth-pkce-js@^4.3.0", "@kinde/jwt-decoder": "npm:@kinde/jwt-decoder@^0.2.0", "@kinde/jwt-validator": "npm:@kinde/jwt-validator@^0.4.0", "@kinde/management-api-js": "npm:@kinde/management-api-js@^0.14.0", "@octokit/rest": "npm:@octokit/rest@^21.1.1", "@std/assert": "jsr:@std/assert@^1.0.11", "@std/async": "jsr:@std/async@^1.0.12", "@std/dotenv": "jsr:@std/dotenv@^0.225.3", "@std/http": "jsr:@std/http@^1.0.13", "@wok/djwt": "jsr:@wok/djwt@^3.0.2", "notiflix": "npm:notiflix@^3.2.8", "types/": "./src/types/", "utils/": "./src/utils/", "components/": "./src/components/", "controllers/": "./src/controllers/", "settings/": "./src/settings/", "libraries/": "./src/libraries/", "styles/": "./src/styles/", "sets/": "./src/components/games/sets/", "dice/": "./src/components/games/playables/dice/", "core/": "./src/components/core/", "public/": "./public/", "TRANSLATIONS": "./src/TRANSLATIONS.ts", "GLOBALS": "./src/GLOBALS.ts", "api/": "./src/api/", "localforage": "npm:localforage@1.10.0", "store": "./src/controllers/store.ts", "@deno/gfm": "jsr:@deno/gfm@0.10.0"}, "tasks": {"build": "deno run --allow-all ./src/scripts/build.ts --build", "develop": "deno run --allow-all --watch ./src/scripts/watch.ts", "alpha-to-beta": "git checkout alpha && git checkout beta && git merge alpha && git push && git checkout alpha", "beta-to-production": "git checkout beta && git checkout production && git merge beta && git push && git checkout beta", "beta-zoom-to-alpha": "git checkout alpha", "test": "deno test --allow-all", "ts-check": "deno check '**/*.ts'", "cache": "deno cache '**/*.ts'", "lint": "deno lint '**/*.ts'"}, "exclude": ["./src/libraries"], "lint": {"rules": {"exclude": ["require-await", "no-namespace"]}}}