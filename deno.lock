{"version": "4", "specifiers": {"jsr:@deno/gfm@0.10.0": "0.10.0", "jsr:@denosaurs/emoji@0.3": "0.3.1", "jsr:@gramio/files@0.2": "0.2.0", "jsr:@gramio/types@^8.1.0": "8.3.3", "jsr:@gramio/types@^8.3.3": "8.3.3", "jsr:@gramio/wrappergram@^1.2.0": "1.3.0", "jsr:@luca/esbuild-deno-loader@~0.11.1": "0.11.1", "jsr:@std/assert@^1.0.11": "1.0.13", "jsr:@std/async@^1.0.12": "1.0.13", "jsr:@std/bytes@^1.0.2": "1.0.6", "jsr:@std/cli@^1.0.17": "1.0.17", "jsr:@std/dotenv@~0.225.3": "0.225.4", "jsr:@std/encoding@0.221": "0.221.0", "jsr:@std/encoding@^1.0.10": "1.0.10", "jsr:@std/encoding@^1.0.5": "1.0.10", "jsr:@std/fmt@^1.0.8": "1.0.8", "jsr:@std/html@^1.0.4": "1.0.4", "jsr:@std/http@^1.0.13": "1.0.16", "jsr:@std/internal@^1.0.6": "1.0.7", "jsr:@std/media-types@^1.1.0": "1.1.0", "jsr:@std/net@^1.0.4": "1.0.4", "jsr:@std/path@^1.0.6": "1.0.9", "jsr:@std/path@^1.0.9": "1.0.9", "jsr:@std/streams@^1.0.9": "1.0.9", "jsr:@wok/djwt@^3.0.2": "3.0.2", "npm:@kinde-oss/kinde-auth-pkce-js@^4.3.0": "4.3.0", "npm:@kinde/jwt-decoder@0.2": "0.2.0", "npm:@kinde/jwt-validator@0.4": "0.4.0", "npm:@kinde/management-api-js@0.14": "0.14.0", "npm:@octokit/rest@^21.1.1": "21.1.1_@octokit+core@6.1.5", "npm:esbuild@0.25.0": "0.25.0", "npm:github-slugger@2": "2.0.0", "npm:he@^1.2.0": "1.2.0", "npm:katex@0.16": "0.16.22", "npm:localforage@1.10.0": "1.10.0", "npm:marked-alert@2": "2.1.2_marked@12.0.2", "npm:marked-footnote@^1.2.0": "1.2.4_marked@12.0.2", "npm:marked-gfm-heading-id@^3.1.0": "3.2.0_marked@12.0.2", "npm:marked@12": "12.0.2", "npm:notiflix@^3.2.8": "3.2.8", "npm:prismjs@^1.29.0": "1.30.0", "npm:sanitize-html@^2.13.0": "2.17.0", "npm:string-ts@*": "2.2.1"}, "jsr": {"@deno/gfm@0.10.0": {"integrity": "51708205e3559a4aeb6afb29d07c5bfafe7941f91bb360351ef6621de9a39527", "dependencies": ["jsr:@denosaurs/emoji", "npm:github-slugger", "npm:he", "npm:katex", "npm:marked", "npm:marked-alert", "npm:marked-footnote", "npm:marked-gfm-heading-id", "npm:prismjs", "npm:sanitize-html"]}, "@denosaurs/emoji@0.3.1": {"integrity": "b0aed5f55dec99e83da7c9637fe0a36d1d6252b7c99deaaa3fc5dea3fcf3da8b"}, "@gramio/files@0.2.0": {"integrity": "0098eb7accdb8bd7c114b875f47b075eb0d89b7427c00a2348934859d6719a71", "dependencies": ["jsr:@gramio/types@^8.1.0"]}, "@gramio/types@8.3.3": {"integrity": "3539d49df2755609c125aa0086cb7ae737aada3e069569aeac05e05cf73786c9"}, "@gramio/wrappergram@1.3.0": {"integrity": "6a59c66cbcfd62f938e7b756f40b64767c32f7c222d77792f95ccc4bfee697d2", "dependencies": ["jsr:@gramio/files", "jsr:@gramio/types@^8.3.3"]}, "@luca/esbuild-deno-loader@0.11.1": {"integrity": "dc020d16d75b591f679f6b9288b10f38bdb4f24345edb2f5732affa1d9885267", "dependencies": ["jsr:@std/bytes", "jsr:@std/encoding@^1.0.5", "jsr:@std/path@^1.0.6"]}, "@std/assert@1.0.13": {"integrity": "ae0d31e41919b12c656c742b22522c32fb26ed0cba32975cb0de2a273cb68b29", "dependencies": ["jsr:@std/internal"]}, "@std/async@1.0.13": {"integrity": "1d76ca5d324aef249908f7f7fe0d39aaf53198e5420604a59ab5c035adc97c96"}, "@std/bytes@1.0.6": {"integrity": "f6ac6adbd8ccd99314045f5703e23af0a68d7f7e58364b47d2c7f408aeb5820a"}, "@std/cli@1.0.17": {"integrity": "e15b9abe629e17be90cc6216327f03a29eae613365f1353837fa749aad29ce7b"}, "@std/dotenv@0.225.4": {"integrity": "2a672c2b192abe535dcfea1ae89f219ee3979af6aad7d185cb19206ee9bc5caf"}, "@std/encoding@0.221.0": {"integrity": "d1dd76ef0dc5d14088411e6dc1dede53bf8308c95d1537df1214c97137208e45"}, "@std/encoding@1.0.10": {"integrity": "8783c6384a2d13abd5e9e87a7ae0520a30e9f56aeeaa3bdf910a3eaaf5c811a1"}, "@std/fmt@1.0.8": {"integrity": "71e1fc498787e4434d213647a6e43e794af4fd393ef8f52062246e06f7e372b7"}, "@std/html@1.0.4": {"integrity": "eff3497c08164e6ada49b7f81a28b5108087033823153d065e3f89467dd3d50e"}, "@std/http@1.0.16": {"integrity": "80c8d08c4bfcf615b89978dcefb84f7e880087cf3b6b901703936f3592a06933", "dependencies": ["jsr:@std/cli", "jsr:@std/encoding@^1.0.10", "jsr:@std/fmt", "jsr:@std/html", "jsr:@std/media-types", "jsr:@std/net", "jsr:@std/path@^1.0.9", "jsr:@std/streams"]}, "@std/internal@1.0.7": {"integrity": "39eeb5265190a7bc5d5591c9ff019490bd1f2c3907c044a11b0d545796158a0f"}, "@std/media-types@1.1.0": {"integrity": "c9d093f0c05c3512932b330e3cc1fe1d627b301db33a4c2c2185c02471d6eaa4"}, "@std/net@1.0.4": {"integrity": "2f403b455ebbccf83d8a027d29c5a9e3a2452fea39bb2da7f2c04af09c8bc852"}, "@std/path@1.0.9": {"integrity": "260a49f11edd3db93dd38350bf9cd1b4d1366afa98e81b86167b4e3dd750129e"}, "@std/streams@1.0.9": {"integrity": "a9d26b1988cdd7aa7b1f4b51e1c36c1557f3f252880fa6cc5b9f37078b1a5035"}, "@wok/djwt@3.0.2": {"integrity": "e1c8afe6e321941edda40d91b089e93738469b994d53f41f82e0e7fcd7dde259", "dependencies": ["jsr:@std/encoding@0.221"]}}, "npm": {"@esbuild/aix-ppc64@0.25.0": {"integrity": "sha512-O7vun9Sf8DFjH2UtqK8Ku3LkquL9SZL8OLY1T5NZkA34+wG3OQF7cl4Ql8vdNzM6fzBbYfLaiRLIOZ+2FOCgBQ=="}, "@esbuild/android-arm64@0.25.0": {"integrity": "sha512-grvv8WncGjDSyUBjN9yHXNt+cq0snxXbDxy5pJtzMKGmmpPxeAmAhWxXI+01lU5rwZomDgD3kJwulEnhTRUd6g=="}, "@esbuild/android-arm@0.25.0": {"integrity": "sha512-PTyWCYYiU0+1eJKmw21lWtC+d08JDZPQ5g+kFyxP0V+es6VPPSUhM6zk8iImp2jbV6GwjX4pap0JFbUQN65X1g=="}, "@esbuild/android-x64@0.25.0": {"integrity": "sha512-m/ix7SfKG5buCnxasr52+LI78SQ+wgdENi9CqyCXwjVR2X4Jkz+BpC3le3AoBPYTC9NHklwngVXvbJ9/Akhrfg=="}, "@esbuild/darwin-arm64@0.25.0": {"integrity": "sha512-mVwdUb5SRkPayVadIOI78K7aAnPamoeFR2bT5nszFUZ9P8UpK4ratOdYbZZXYSqPKMHfS1wdHCJk1P1EZpRdvw=="}, "@esbuild/darwin-x64@0.25.0": {"integrity": "sha512-DgDaYsPWFTS4S3nWpFcMn/33ZZwAAeAFKNHNa1QN0rI4pUjgqf0f7ONmXf6d22tqTY+H9FNdgeaAa+YIFUn2Rg=="}, "@esbuild/freebsd-arm64@0.25.0": {"integrity": "sha512-VN4ocxy6dxefN1MepBx/iD1dH5K8qNtNe227I0mnTRjry8tj5MRk4zprLEdG8WPyAPb93/e4pSgi1SoHdgOa4w=="}, "@esbuild/freebsd-x64@0.25.0": {"integrity": "sha512-mrSgt7lCh07FY+hDD1TxiTyIHyttn6vnjesnPoVDNmDfOmggTLXRv8Id5fNZey1gl/V2dyVK1VXXqVsQIiAk+A=="}, "@esbuild/linux-arm64@0.25.0": {"integrity": "sha512-9QAQjTWNDM/Vk2bgBl17yWuZxZNQIF0OUUuPZRKoDtqF2k4EtYbpyiG5/Dk7nqeK6kIJWPYldkOcBqjXjrUlmg=="}, "@esbuild/linux-arm@0.25.0": {"integrity": "sha512-vkB3IYj2IDo3g9xX7HqhPYxVkNQe8qTK55fraQyTzTX/fxaDtXiEnavv9geOsonh2Fd2RMB+i5cbhu2zMNWJwg=="}, "@esbuild/linux-ia32@0.25.0": {"integrity": "sha512-43ET5bHbphBegyeqLb7I1eYn2P/JYGNmzzdidq/w0T8E2SsYL1U6un2NFROFRg1JZLTzdCoRomg8Rvf9M6W6Gg=="}, "@esbuild/linux-loong64@0.25.0": {"integrity": "sha512-fC95c/xyNFueMhClxJmeRIj2yrSMdDfmqJnyOY4ZqsALkDrrKJfIg5NTMSzVBr5YW1jf+l7/cndBfP3MSDpoHw=="}, "@esbuild/linux-mips64el@0.25.0": {"integrity": "sha512-nkAMFju7KDW73T1DdH7glcyIptm95a7Le8irTQNO/qtkoyypZAnjchQgooFUDQhNAy4iu08N79W4T4pMBwhPwQ=="}, "@esbuild/linux-ppc64@0.25.0": {"integrity": "sha512-NhyOejdhRGS8Iwv+KKR2zTq2PpysF9XqY+Zk77vQHqNbo/PwZCzB5/h7VGuREZm1fixhs4Q/qWRSi5zmAiO4Fw=="}, "@esbuild/linux-riscv64@0.25.0": {"integrity": "sha512-5S/rbP5OY+GHLC5qXp1y/Mx//e92L1YDqkiBbO9TQOvuFXM+iDqUNG5XopAnXoRH3FjIUDkeGcY1cgNvnXp/kA=="}, "@esbuild/linux-s390x@0.25.0": {"integrity": "sha512-XM2BFsEBz0Fw37V0zU4CXfcfuACMrppsMFKdYY2WuTS3yi8O1nFOhil/xhKTmE1nPmVyvQJjJivgDT+xh8pXJA=="}, "@esbuild/linux-x64@0.25.0": {"integrity": "sha512-9yl91rHw/cpwMCNytUDxwj2XjFpxML0y9HAOH9pNVQDpQrBxHy01Dx+vaMu0N1CKa/RzBD2hB4u//nfc+Sd3Cw=="}, "@esbuild/netbsd-arm64@0.25.0": {"integrity": "sha512-RuG4PSMPFfrkH6UwCAqBzauBWTygTvb1nxWasEJooGSJ/NwRw7b2HOwyRTQIU97Hq37l3npXoZGYMy3b3xYvPw=="}, "@esbuild/netbsd-x64@0.25.0": {"integrity": "sha512-jl+qisSB5jk01N5f7sPCsBENCOlPiS/xptD5yxOx2oqQfyourJwIKLRA2yqWdifj3owQZCL2sn6o08dBzZGQzA=="}, "@esbuild/openbsd-arm64@0.25.0": {"integrity": "sha512-21sUNbq2r84YE+SJDfaQRvdgznTD8Xc0oc3p3iW/a1EVWeNj/SdUCbm5U0itZPQYRuRTW20fPMWMpcrciH2EJw=="}, "@esbuild/openbsd-x64@0.25.0": {"integrity": "sha512-2gwwriSMPcCFRlPlKx3zLQhfN/2WjJ2NSlg5TKLQOJdV0mSxIcYNTMhk3H3ulL/cak+Xj0lY1Ym9ysDV1igceg=="}, "@esbuild/sunos-x64@0.25.0": {"integrity": "sha512-bxI7ThgLzPrPz484/S9jLlvUAHYMzy6I0XiU1ZMeAEOBcS0VePBFxh1JjTQt3Xiat5b6Oh4x7UC7IwKQKIJRIg=="}, "@esbuild/win32-arm64@0.25.0": {"integrity": "sha512-ZUAc2YK6JW89xTbXvftxdnYy3m4iHIkDtK3CLce8wg8M2L+YZhIvO1DKpxrd0Yr59AeNNkTiic9YLf6FTtXWMw=="}, "@esbuild/win32-ia32@0.25.0": {"integrity": "sha512-eSNxISBu8XweVEWG31/JzjkIGbGIJN/TrRoiSVZwZ6pkC6VX4Im/WV2cz559/TXLcYbcrDN8JtKgd9DJVIo8GA=="}, "@esbuild/win32-x64@0.25.0": {"integrity": "sha512-ZENoHJBxA20C2zFzh6AI4fT6RraMzjYw4xKWemRTRmRVtN9c5DcH9r/f2ihEkMjOW5eGgrwCslG/+Y/3bL+DHQ=="}, "@kinde-oss/kinde-auth-pkce-js@4.3.0": {"integrity": "sha512-IcqENVE22/5Ziy0C5riXfB3qi4kQgMlQea6sb+rV7rMKspFBYVVcLl1LnLRehtZBNBNsmnKwa3lUTw4OnptnpQ==", "dependencies": ["jwt-decode"]}, "@kinde/jwt-decoder@0.2.0": {"integrity": "sha512-dqtwCmAvywOVLkkUfp4UbqdvVLsK0cvHsJhU3gDY9rgjAdZhGw0vCreBW6j3MFLxbi6cZm7pMU7/O5SJgvN5Rw=="}, "@kinde/jwt-validator@0.4.0": {"integrity": "sha512-aseXLTD/rh/rZ2v85Xy493CEtuC49MA4Hbt6ObccqSJfIGLAeMrAtBh2m9DleigVkMuZ/99/U4PqLnaVDLt5OQ==", "dependencies": ["@kinde/jwt-decoder", "jsrsasign"]}, "@kinde/management-api-js@0.14.0": {"integrity": "sha512-4BUsr7+eHI9hIFGkqasmQ5oUAYiM2LaKmQVyOB4Gf5K+7DToHXiHZCmlgB/syeu3cBzItj04zLgOjEdg1lkXNA==", "dependencies": ["@kinde/jwt-decoder", "aws-jwt-verify", "dotenv"]}, "@octokit/auth-token@5.1.2": {"integrity": "sha512-JcQDsBdg49Yky2w2ld20IHAlwr8d/d8N6NiOXbtuoPCqzbsiJgF633mVUw3x4mo0H5ypataQIX7SFu3yy44Mpw=="}, "@octokit/core@6.1.5": {"integrity": "sha512-vvmsN0r7rguA+FySiCsbaTTobSftpIDIpPW81trAmsv9TGxg3YCujAxRYp/Uy8xmDgYCzzgulG62H7KYUFmeIg==", "dependencies": ["@octokit/auth-token", "@octokit/graphql", "@octokit/request", "@octokit/request-error", "@octokit/types@14.0.0", "before-after-hook", "universal-user-agent"]}, "@octokit/endpoint@10.1.4": {"integrity": "sha512-OlYOlZIsfEVZm5HCSR8aSg02T2lbUWOsCQoPKfTXJwDzcHQBrVBGdGXb89dv2Kw2ToZaRtudp8O3ZIYoaOjKlA==", "dependencies": ["@octokit/types@14.0.0", "universal-user-agent"]}, "@octokit/graphql@8.2.2": {"integrity": "sha512-Yi8hcoqsrXGdt0yObxbebHXFOiUA+2v3n53epuOg1QUgOB6c4XzvisBNVXJSl8RYA5KrDuSL2yq9Qmqe5N0ryA==", "dependencies": ["@octokit/request", "@octokit/types@14.0.0", "universal-user-agent"]}, "@octokit/openapi-types@24.2.0": {"integrity": "sha512-9sIH3nSUttelJSXUrmGzl7QUBFul0/mB8HRYl3fOlgHbIWG+WnYDXU3v/2zMtAvuzZ/ed00Ei6on975FhBfzrg=="}, "@octokit/openapi-types@25.0.0": {"integrity": "sha512-FZvktFu7HfOIJf2BScLKIEYjDsw6RKc7rBJCdvCTfKsVnx2GEB/Nbzjr29DUdb7vQhlzS/j8qDzdditP0OC6aw=="}, "@octokit/plugin-paginate-rest@11.6.0_@octokit+core@6.1.5": {"integrity": "sha512-n5KPteiF7pWKgBIBJSk8qzoZWcUkza2O6A0za97pMGVrGfPdltxrfmfF5GucHYvHGZD8BdaZmmHGz5cX/3gdpw==", "dependencies": ["@octokit/core", "@octokit/types@13.10.0"]}, "@octokit/plugin-request-log@5.3.1_@octokit+core@6.1.5": {"integrity": "sha512-n/lNeCtq+9ofhC15xzmJCNKP2BWTv8Ih2TTy+jatNCCq/gQP/V7rK3fjIfuz0pDWDALO/o/4QY4hyOF6TQQFUw==", "dependencies": ["@octokit/core"]}, "@octokit/plugin-rest-endpoint-methods@13.5.0_@octokit+core@6.1.5": {"integrity": "sha512-9Pas60Iv9ejO3WlAX3maE1+38c5nqbJXV5GrncEfkndIpZrJ/WPMRd2xYDcPPEt5yzpxcjw9fWNoPhsSGzqKqw==", "dependencies": ["@octokit/core", "@octokit/types@13.10.0"]}, "@octokit/request-error@6.1.8": {"integrity": "sha512-WEi/R0Jmq+IJKydWlKDmryPcmdYSVjL3ekaiEL1L9eo1sUnqMJ+grqmC9cjk7CA7+b2/T397tO5d8YLOH3qYpQ==", "dependencies": ["@octokit/types@14.0.0"]}, "@octokit/request@9.2.3": {"integrity": "sha512-Ma+pZU8PXLOEYzsWf0cn/gY+ME57Wq8f49WTXA8FMHp2Ps9djKw//xYJ1je8Hm0pR2lU9FUGeJRWOtxq6olt4w==", "dependencies": ["@octokit/endpoint", "@octokit/request-error", "@octokit/types@14.0.0", "fast-content-type-parse", "universal-user-agent"]}, "@octokit/rest@21.1.1_@octokit+core@6.1.5": {"integrity": "sha512-sTQV7va0IUVZcntzy1q3QqPm/r8rWtDCqpRAmb8eXXnKkjoQEtFe3Nt5GTVsHft+R6jJoHeSiVLcgcvhtue/rg==", "dependencies": ["@octokit/core", "@octokit/plugin-paginate-rest", "@octokit/plugin-request-log", "@octokit/plugin-rest-endpoint-methods"]}, "@octokit/types@13.10.0": {"integrity": "sha512-ifLaO34EbbPj0Xgro4G5lP5asESjwHracYJvVaPIyXMuiuXLlhic3S47cBdTb+jfODkTE5YtGCLt3Ay3+J97sA==", "dependencies": ["@octokit/openapi-types@24.2.0"]}, "@octokit/types@14.0.0": {"integrity": "sha512-VVmZP0lEhbo2O1pdq63gZFiGCKkm8PPp8AUOijlwPO6hojEVjspA0MWKP7E4hbvGxzFKNqKr6p0IYtOH/Wf/zA==", "dependencies": ["@octokit/openapi-types@25.0.0"]}, "aws-jwt-verify@5.1.0": {"integrity": "sha512-98ioOBMyrLU5jW5rPvkJo20XlNB2rAX3tZR3BM6AamfBkOoSRLV1EyGkbgHQzgFOWyQ7yV8+tce6M24rOpMkgw=="}, "before-after-hook@3.0.2": {"integrity": "sha512-Nik3Sc0ncrMK4UUdXQmAnRtzmNQTAAXmXIopizwZ1W1t8QmfJj+zL4OA2I7XPTPW5z5TDqv4hRo/JzouDJnX3A=="}, "commander@8.3.0": {"integrity": "sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww=="}, "deepmerge@4.3.1": {"integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A=="}, "dom-serializer@2.0.0": {"integrity": "sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==", "dependencies": ["domelementtype", "<PERSON><PERSON><PERSON><PERSON>", "entities"]}, "domelementtype@2.3.0": {"integrity": "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw=="}, "domhandler@5.0.3": {"integrity": "sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==", "dependencies": ["domelementtype"]}, "domutils@3.2.2": {"integrity": "sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==", "dependencies": ["dom-serializer", "domelementtype", "<PERSON><PERSON><PERSON><PERSON>"]}, "dotenv@16.5.0": {"integrity": "sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg=="}, "entities@4.5.0": {"integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw=="}, "esbuild@0.25.0": {"integrity": "sha512-BXq5mqc8ltbaN34cDqWuYKyNhX8D/Z0J1xdtdQ8UcIIIyJyz+ZMKUt58tF3SrZ85jcfN/PZYhjR5uDQAYNVbuw==", "dependencies": ["@esbuild/aix-ppc64", "@esbuild/android-arm", "@esbuild/android-arm64", "@esbuild/android-x64", "@esbuild/darwin-arm64", "@esbuild/darwin-x64", "@esbuild/freebsd-arm64", "@esbuild/freebsd-x64", "@esbuild/linux-arm", "@esbuild/linux-arm64", "@esbuild/linux-ia32", "@esbuild/linux-loong64", "@esbuild/linux-mips64el", "@esbuild/linux-ppc64", "@esbuild/linux-riscv64", "@esbuild/linux-s390x", "@esbuild/linux-x64", "@esbuild/netbsd-arm64", "@esbuild/netbsd-x64", "@esbuild/openbsd-arm64", "@esbuild/openbsd-x64", "@esbuild/sunos-x64", "@esbuild/win32-arm64", "@esbuild/win32-ia32", "@esbuild/win32-x64"]}, "escape-string-regexp@4.0.0": {"integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="}, "fast-content-type-parse@2.0.1": {"integrity": "sha512-nGqtvLrj5w0naR6tDPfB4cUmYCqouzyQiz6C5y/LtcDllJdrcc6WaWW6iXyIIOErTa/XRybj28aasdn4LkVk6Q=="}, "github-slugger@2.0.0": {"integrity": "sha512-IaOQ9puYtjrkq7Y0Ygl9KDZnrf/aiUJYUpVf89y8kyaxbRG7Y1SrX/jaumrv81vc61+kiMempujsM3Yw7w5qcw=="}, "he@1.2.0": {"integrity": "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw=="}, "htmlparser2@8.0.2": {"integrity": "sha512-GYdjWKDkbRLkZ5geuHs5NY1puJ+PXwP7+fHPRz06Eirsb9ugf6d8kkXav6ADhcODhFFPMIXyxkxSuMf3D6NCFA==", "dependencies": ["domelementtype", "<PERSON><PERSON><PERSON><PERSON>", "domutils", "entities"]}, "immediate@3.0.6": {"integrity": "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ=="}, "is-plain-object@5.0.0": {"integrity": "sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q=="}, "jsrsasign@11.1.0": {"integrity": "sha512-Ov74K9GihaK9/9WncTe1mPmvrO7Py665TUfUKvraXBpu+xcTWitrtuOwcjf4KMU9maPaYn0OuaWy0HOzy/GBXg=="}, "jwt-decode@4.0.0": {"integrity": "sha512-+KJGIyHgkGuIq3IEBNftfhW/LfWhXUIY6OmyVWjliu5KH1y0fw7VQ8YndE2O4qZdMSd9SqbnC8GOcZEy0Om7sA=="}, "katex@0.16.22": {"integrity": "sha512-XCHRdUw4lf3SKBaJe4EvgqIuWwkPSo9XoeO8GjQW94Bp7TWv9hNhzZjZ+OH9yf1UmLygb7DIT5GSFQiyt16zYg==", "dependencies": ["commander"]}, "lie@3.1.1": {"integrity": "sha512-RiNhHysUjhrDQntfYSfY4MU24coXXdEOgw9WGcKHNeEwffDYbF//u87M1EWaMGzuFoSbqW0C9C6lEEhDOAswfw==", "dependencies": ["immediate"]}, "localforage@1.10.0": {"integrity": "sha512-14/H1aX7hzBBmmh7sGPd+AOMkkIrHM3Z1PAyGgZigA1H1p5O5ANnMyWzvpAETtG68/dC4pC0ncy3+PPGzXZHPg==", "dependencies": ["lie"]}, "marked-alert@2.1.2_marked@12.0.2": {"integrity": "sha512-EFNRZ08d8L/iEIPLTlQMDjvwIsj03gxWCczYTht6DCiHJIZhMk4NK5gtPY9UqAYb09eV5VGT+jD4lp396E0I+w==", "dependencies": ["marked"]}, "marked-footnote@1.2.4_marked@12.0.2": {"integrity": "sha512-DB2Kl+wFh6YwZd70qABMY6WUkG1UuyqoNTFoDfGyG79Pz24neYtLBkB+45a7o72V7gkfvbC3CGzIYFobxfMT1Q==", "dependencies": ["marked"]}, "marked-gfm-heading-id@3.2.0_marked@12.0.2": {"integrity": "sha512-Xfxpr5lXLDLY10XqzSCA9l2dDaiabQUgtYM9hw8yunyVsB/xYBRpiic6BOiY/EAJw1ik1eWr1ET1HKOAPZBhXg==", "dependencies": ["github-slugger", "marked"]}, "marked@12.0.2": {"integrity": "sha512-qXUm7e/YKFoqFPYPa3Ukg9xlI5cyAtGmyEIzMfW//m6kXwCy2Ps9DYf5ioijFKQ8qyuscrHoY04iJGctu2Kg0Q=="}, "nanoid@3.3.11": {"integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w=="}, "notiflix@3.2.8": {"integrity": "sha512-BNBGr3qNIBNt6NZYvch0OLF9DEEgWKcjYzISMH9eECKwfsUaINb5K/RA21hy5kmfOtdbdAdODrqxts+0xp+xwA=="}, "parse-srcset@1.0.2": {"integrity": "sha512-/2qh0lav6CmI15FzA3i/2Bzk2zCgQhGMkvhOhKNcBVQ1ldgpbfiNTVslmooUmWJcADi1f1kIeynbDRVzNlfR6Q=="}, "picocolors@1.1.1": {"integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="}, "postcss@8.5.3": {"integrity": "sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==", "dependencies": ["nanoid", "picocolors", "source-map-js"]}, "prismjs@1.30.0": {"integrity": "sha512-DEvV2ZF2r2/63V+tK8hQvrR2ZGn10srHbXviTlcv7Kpzw8jWiNTqbVgjO3IY8RxrrOUF8VPMQQFysYYYv0YZxw=="}, "sanitize-html@2.17.0": {"integrity": "sha512-dLAADUSS8rBwhaevT12yCezvioCA+bmUTPH/u57xKPT8d++voeYE6HeluA/bPbQ15TwDBG2ii+QZIEmYx8VdxA==", "dependencies": ["deepmerge", "escape-string-regexp", "htmlparser2", "is-plain-object", "parse-srcset", "postcss"]}, "source-map-js@1.2.1": {"integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA=="}, "string-ts@2.2.1": {"integrity": "sha512-Q2u0gko67PLLhbte5HmPfdOjNvUKbKQM+mCNQae6jE91DmoFHY6HH9GcdqCeNx87DZ2KKjiFxmA0R/42OneGWw=="}, "universal-user-agent@7.0.3": {"integrity": "sha512-TmnEAEAsBJVZM/AADELsK76llnwcf9vMKuPz8JflO1frO8Lchitr0fNaN9d+Ap0BjKtqWqd/J17qeDnXh8CL2A=="}}, "workspace": {"dependencies": ["jsr:@deno/gfm@0.10.0", "jsr:@gramio/wrappergram@^1.2.0", "jsr:@std/assert@^1.0.11", "jsr:@std/async@^1.0.12", "jsr:@std/dotenv@~0.225.3", "jsr:@std/http@^1.0.13", "jsr:@wok/djwt@^3.0.2", "npm:@kinde-oss/kinde-auth-pkce-js@^4.3.0", "npm:@kinde/jwt-decoder@0.2", "npm:@kinde/jwt-validator@0.4", "npm:@kinde/management-api-js@0.14", "npm:@octokit/rest@^21.1.1", "npm:localforage@1.10.0", "npm:notiflix@^3.2.8"]}}