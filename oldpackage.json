{"name": "rpg-meet", "manifest_name": "RPG Meet - <PERSON><PERSON> | Music | Webcam Tools", "version": "3.2.17", "version_name": "3.2.17", "description": "Add RPG tools to your session: roll dice, draw tokens, add webcam overlays, listen to the same audio in real time, and much more!", "author": "marc<PERSON><PERSON>", "license": "UNLICENSED", "scripts": {"watch": "pnpm cross-env-shell NODE_ENV=development \"pnpm utility:clean && pnpm build:mirrormanifest && pnpm parcel watch src/manifest.json --host localhost --target webext-dev\"", "build": "pnpm cross-env-shell NODE_ENV=production \"pnpm utility:clean && pnpm build:mirrormanifest && pnpm parcel build --no-source-maps src/manifest.json --target webext-prod\"", "utility:clean": "pnpm rimraf dist && pnpm rimraf .parcel-cache && pnpm rimraf export", "build:mirrormanifest": "pnpm tsx .github/workflows/scripts/mirror-manifest.ts", "build:zip": "pnpm tsx .github/workflows/scripts/zip.ts", "format:write": "pnpm prettier -w src", "format:check": "pnpm prettier -c src", "stats": "bash stats/git-stats.sh", "preinstall": "npx only-allow pnpm"}, "targets": {"webext-dev": {"sourceMap": {"inline": true, "inlineSources": true}}, "webext-prod": {}}, "browserslist": ["last 2 chrome version"], "@parcel/bundler-default": {"minBundles": 10000000, "minBundleSize": 3000, "maxParallelRequests": 20}}