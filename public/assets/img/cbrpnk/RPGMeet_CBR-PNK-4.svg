<svg data-name="CBR+PNK Die 4" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
  viewBox="0 0 48 48">
  <defs>
    <style>
      .cbrpnk-4-cls-1 {
        fill: url(#cbrpnk-gradient-4);
      }
      .cbrpnk-4-cls-2 {
        fill: #1a1a1a;
        stroke: #ffff00;
        stroke-width: 1px;
      }
      .cbrpnk-4-cls-3 {
        fill: #ffff00;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        font-weight: bold;
      }
      .cbrpnk-4-cls-4 {
        fill: none;
        stroke: #ff0080;
        stroke-width: 0.5px;
        opacity: 0.6;
      }
    </style>
    <linearGradient id="cbrpnk-gradient-4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2a2a2a" />
      <stop offset="50%" stop-color="#1a1a1a" />
      <stop offset="100%" stop-color="#0a0a0a" />
    </linearGradient>
  </defs>
  
  <!-- Main die body -->
  <rect class="cbrpnk-4-cls-1" x="5.8467" y="5.8467" width="36.3065" height="36.3065" rx="3.0072" ry="3.0072" />
  <rect class="cbrpnk-4-cls-2" x="6.9651" y="6.9651" width="34.0698" height="34.0698" rx="8.1482" ry="8.1482" />
  
  <!-- Circuit pattern background -->
  <path class="cbrpnk-4-cls-4" d="M12,12 L36,12 M12,16 L20,16 M28,16 L36,16 M12,20 L16,20 M32,20 L36,20 M12,24 L36,24 M12,28 L20,28 M28,28 L36,28 M12,32 L16,32 M32,32 L36,32 M12,36 L36,36" />
  
  <!-- Four dots for "4" in corners -->
  <circle class="cbrpnk-4-cls-3" cx="16" cy="16" r="2.5" />
  <circle class="cbrpnk-4-cls-3" cx="32" cy="16" r="2.5" />
  <circle class="cbrpnk-4-cls-3" cx="16" cy="32" r="2.5" />
  <circle class="cbrpnk-4-cls-3" cx="32" cy="32" r="2.5" />
  
  <!-- Glitch effect lines -->
  <rect class="cbrpnk-4-cls-4" x="14" y="15" width="6" height="0.5" opacity="0.8" />
  <rect class="cbrpnk-4-cls-4" x="30" y="15" width="6" height="0.5" opacity="0.8" />
  <rect class="cbrpnk-4-cls-4" x="14" y="31" width="6" height="0.5" opacity="0.6" />
  <rect class="cbrpnk-4-cls-4" x="30" y="31" width="6" height="0.5" opacity="0.6" />
</svg>
