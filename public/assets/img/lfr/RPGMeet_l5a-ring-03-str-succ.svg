<svg id="uuid-52d1a4b0-63c5-4b56-a7df-37e3639a5b41" data-name="Livello 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48"> <defs> <style> .uuid-0d83040d-0a2a-4ef1-a35c-a648ec3412c5 { fill: #fff; } .uuid-056ec974-800f-4764-8d3e-b7f4c9d68366 { fill: url(#uuid-b06fb2dd-e703-4b5e-9949-7a1f692bb42a); } .uuid-7883007c-abb2-4299-8f46-242f302407ae { fill: none; } .uuid-eff1a355-305c-4763-a6e8-c19878118a9c { fill: #454545; } </style> <linearGradient id="uuid-b06fb2dd-e703-4b5e-9949-7a1f692bb42a" data-name="Sfumatura senza nome 24" x1="9.5099" y1="9.5099" x2="37.3145" y2="37.3145" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" gradientUnits="userSpaceOnUse"> <stop offset="0" stop-color="#606060"/> <stop offset="1" stop-color="#323232"/> </linearGradient> </defs> <rect class="uuid-7883007c-abb2-4299-8f46-242f302407ae" width="48" height="48"/> <g> <g> <rect class="uuid-056ec974-800f-4764-8d3e-b7f4c9d68366" x="5.8467" y="5.8467" width="36.3065" height="36.3065" rx="3.0072" ry="3.0072"/> <rect class="uuid-eff1a355-305c-4763-a6e8-c19878118a9c" x="6.9651" y="6.9651" width="34.0698" height="34.0698" rx="8.1482" ry="8.1482"/> </g> <path class="uuid-0d83040d-0a2a-4ef1-a35c-a648ec3412c5" d="M35.3793,19.5041l.2121-.3438c.0737-.1089,.1469-.2538,.2298-.425,.0411-.0857,.0904-.175,.1334-.2748l.1256-.3226c.35-.8966,.6047-2.3356,.1582-3.9128-.2188-.7826-.6337-1.5737-1.2264-2.2684-.5876-.7-1.3797-1.2834-2.2848-1.6458-.4472-.1948-.9337-.3033-1.4223-.3808-.2482-.0187-.4942-.0585-.7451-.0458-.1249,.0033-.2497-.0061-.3744,.0065l-.3737,.037c-.4951,.0694-.988,.1828-1.4475,.3832-.236,.0835-.4524,.2117-.6733,.3255-.1112,.0557-.2094,.1354-.3146,.2008-.1024,.0704-.2111,.1306-.3034,.2151-.78,.6121-1.4072,1.4118-1.751,2.3011-.3682,.8802-.4596,1.8346-.3486,2.7141,.234,1.7775,1.3532,3.1682,2.5567,3.8163,.5963,.3365,1.2032,.5176,1.7513,.5932,.5494,.0743,1.0387,.054,1.4382-.0083,.8008-.1336,1.2401-.3754,1.3191-.3994,.3098-.1293,.6582-.1103,1.0274,.0094,.3598,.1145,.7523,.3481,1.0277,.7106s.3628,.8043,.2293,1.2553c-.134,.4399-.5081,.8622-1.041,1.0814-.1309,.0413-.9158,.3866-2.2498,.4853-.6644,.0435-1.4624,.0059-2.3307-.1955-.8661-.2016-1.802-.5811-2.6784-1.1907-.8763-.6065-1.6869-1.4358-2.279-2.4722-.5905-1.0307-.9783-2.2464-1.02-3.5115-.0443-1.2617,.217-2.5673,.8243-3.7175,.584-1.1624,1.5078-2.1362,2.5863-2.8291,.1296-.0947,.276-.1637,.4158-.2414,.1428-.0728,.2783-.1583,.4265-.2184,.2951-.1227,.5845-.254,.8914-.3352,.6015-.1958,1.2276-.2831,1.8422-.3106l.4609-.0028c.1528-.0002,.305,.0224,.4562,.0331,.3037,.0154,.5989,.0864,.8923,.1387,.5791,.1448,1.1405,.3301,1.6463,.6054,1.0236,.5245,1.8616,1.2785,2.4495,2.1229,.5941,.8413,.9649,1.7537,1.1241,2.6197,.1623,.8671,.1383,1.6779,.0201,2.3699-.1125,.6955-.3365,1.269-.5495,1.7172l-.1576,.3155c-.0528,.0971-.1111,.1834-.1605,.2659-.0994,.1649-.186,.3029-.2702,.4059l-.2431,.3235Z"/> <path class="uuid-0d83040d-0a2a-4ef1-a35c-a648ec3412c5" d="M20.0211,22.8196c.5177,.4154,.9351,.9032,1.2471,1.4587,1.1446,2.0378,.9682,4.1377-.5394,6.4199-.3752,.568-.8356,1.0995-1.3231,1.6623-.2253,.2601-.452,.5219-.672,.7896-.1012,.1231-.116,.2959-.0373,.4344,.0691,.1216,.1976,.1947,.3345,.1947,.0191,0,.0385-.0014,.0578-.0044,1.1344-.1721,2.1357-.6192,2.9759-1.329,.6076-.5133,.9835-1.1262,1.1917-1.9719,1.1498,1.9583,1.2483,3.924,.2926,5.8436-.5169,1.0383-1.4149,1.7583-2.6279,2.1184,.9205-.8442,1.3827-1.9607,1.4334-3.4225,.0042-.1211-.0489-.2371-.1433-.3131-.0691-.0556-.1545-.0851-.2413-.0851-.0318,0-.0637,.0039-.0952,.012-1.3294,.3394-1.9735,.3576-2.865,.3828l-.2402,.007c-.0698,.0021-.1375,.0033-.2032,.0033-1.086,0-1.3522-.2974-1.4853-.883-.113-.4971,.3769-1.9868,1.1096-3.0169,.0914-.1285,.1905-.2545,.2905-.3819,.2762-.3518,.5617-.7156,.7505-1.1781,.0591-.1447,.0247-.3108-.0869-.4202-.0734-.072-.1707-.11-.2695-.11-.0514,0-.1032,.0103-.1523,.0315-1.5398,.6644-3.2503,1.5136-4.262,2.7747-.8919,1.1118-1.2085,2.4681-1.3428,3.5471-.0298-.0406-.0592-.0822-.0895-.1252-.1552-.2203-.3312-.47-.6722-.7262-.0677-.0509-.149-.0772-.2312-.0772-.0481,0-.0964,.009-.1425,.0274-.1247,.0497-.2145,.1608-.2369,.2932-.1362,.8043,.0938,1.6007,.7029,2.4348,.2082,.2851,.4302,.5571,.6628,.8304-.3866-.2051-.7337-.4646-1.0465-.7812-1.1756-1.1902-1.5991-2.5861-1.3326-4.3929,.0416-.2819,.1165-.5645,.2267-.8538l.6958,.9793c.0734,.1033,.1912,.162,.3137,.162,.0352,0,.0709-.0049,.1059-.0149l.065-.0186,.065-.0186c.1517-.0434,.2615-.1751,.277-.3321,.0116-.1171,.0216-.2344,.0315-.3516,.0214-.2517,.0416-.4895,.0773-.7266,.2319-1.5378,.5203-2.9124,1.7056-4.1626,.2513-.2651,.5238-.538,.833-.8344,.3522-.3376,.7286-.6984,1.019-1.1164-.0667,.8131-.5427,1.5904-1.067,2.4464l-.1722,.2822c-.0899,.1484-.0694,.3386,.0501,.4644,.0746,.0785,.1762,.1198,.2791,.1198,.062,0,.1246-.015,.182-.0459,1.3028-.7001,2.2271-1.403,2.9088-2.2119,.4676-.5548,.8824-1.1501,1.233-1.7694,.4071-.719,.56-1.4025,.4589-2.044"/> </g> </svg>