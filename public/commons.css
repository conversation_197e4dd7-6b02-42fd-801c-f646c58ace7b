* {
  box-sizing: border-box;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}
html,body{
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  color: white;
  overflow: hidden;
}

input {
  color: black;
  border-bottom-width: 2px;
}

input:focus {
  border-bottom-color: #00796b;
}

[rpgmeet-tooltip]:hover:after {
  display: inline-block;
  z-index: 10000;
  content: attr(rpgmeet-tooltip);
  font-size: x-small;
  vertical-align: top;
  line-height: 1em;
}

[suit^="♥️"] {
  text-shadow: 1px 1px red;
}
[suit^="♣️"] {
  text-shadow: 1px 1px blue;
}
[suit^="♦️"] {
  text-shadow: 1px 1px red;
}
[suit^="♠️"] {
  text-shadow: 1px 1px blue;
}
[suit^="🃏"] {
  text-shadow: 1px 1px green;
}
.card-details-black {
  fill: #444;
}
.card-details-red {
  fill: #9e3030;
}
.card-background {
  fill: #f2f2f2;
}

[rpg-meet-translation-value]::after {
  content: attr(rpg-meet-translation-value);
}
[collapser_legend][rpg-meet-translation-value]::after {
  content: attr(rpg-meet-translation-value) "▼";
}

fieldset {
  border-radius: 0.25rem;
}

details > summary {
  list-style-type: "";
  background: transparent
    url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIj48cGF0aCBkPSJNNDQxLjkgMTY3LjNsLTE5LjgtMTkuOGMtNC43LTQuNy0xMi4zLTQuNy0xNyAwTDIyNCAzMjguMiA0Mi45IDE0Ny41Yy00LjctNC43LTEyLjMtNC43LTE3IDBMNi4xIDE2Ny4zYy00LjcgNC43LTQuNyAxMi4zIDAgMTdsMjA5LjQgMjA5LjRjNC43IDQuNyAxMi4zIDQuNyAxNyAwbDIwOS40LTIwOS40YzQuNy00LjcgNC43LTEyLjMgMC0xN3oiIGZpbGw9IiM2NjY2NjYiLz48L3N2Zz4NCg==")
    center right 20px / 16px no-repeat;
}

details[open] > summary {
  list-style-type: "";
  background: transparent
    url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIj48cGF0aCBkPSJNNi4xMDEgMzU5LjI5M0wyNS45IDM3OS4wOTJjNC42ODYgNC42ODYgMTIuMjg0IDQuNjg2IDE2Ljk3MSAwTDIyNCAxOTguMzkzbDE4MS4xMyAxODAuNjk4YzQuNjg2IDQuNjg2IDEyLjI4NCA0LjY4NiAxNi45NzEgMGwxOS43OTktMTkuNzk5YzQuNjg2LTQuNjg2IDQuNjg2LTEyLjI4NCAwLTE2Ljk3MUwyMzIuNDg1IDEzMi45MDhjLTQuNjg2LTQuNjg2LTEyLjI4NC00LjY4Ni0xNi45NzEgMEw2LjEwMSAzNDIuMzIyYy00LjY4NyA0LjY4Ny00LjY4NyAxMi4yODUgMCAxNi45NzF6IiBmaWxsPSIjNjY2NjY2Ii8+PC9zdmc+DQo=")
    center right 20px / 16px no-repeat;
}

[connections]::after {
  content: attr(connections);
  color: green;
  margin-left: 0.25rem;
}

img,
button,
a,
svg {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
  -webkit-user-drag: none;
}

label[for] {
  cursor: pointer;
}

.grid-rows-12 {
  grid-template-rows: repeat(12, minmax(0, 1fr));
}
.row-start-8 {
  grid-row-start: 8;
}
.row-start-9 {
  grid-row-start: 9;
}
.row-start-10 {
  grid-row-start: 10;
}
.row-start-11 {
  grid-row-start: 11;
}
.row-start-12 {
  grid-row-start: 12;
}
.row-end-8 {
  grid-row-end: 8;
}
.row-end-9 {
  grid-row-end: 9;
}
.row-end-10 {
  grid-row-end: 10;
}
.row-end-11 {
  grid-row-end: 11;
}
.row-end-12 {
  grid-row-end: 12;
}
.row-end-13 {
  grid-row-end: 13;
}
.row-span-7 {
  grid-row: span 7 / span 7;
}
.row-span-8 {
  grid-row: span 8 / span 8;
}
.row-span-9 {
  grid-row: span 9 / span 9;
}
.row-span-10 {
  grid-row: span 10 / span 10;
}
.row-span-11 {
  grid-row: span 11 / span 11;
}
.row-span-12 {
  grid-row: span 12 / span 12;
}

.oswald{font-family:'Oswald'}
