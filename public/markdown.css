/* Markdown styling for RPG Meet */

/* Base styles to match the webapp */
body {
  box-sizing: border-box;
  font-family: '<PERSON>', sans-serif;
  background: linear-gradient(278deg, #252121, #4c1212, #210101);
  background-size: 180% 180%;
  animation: gradient-animation 24s ease infinite;
  color: white;
  line-height: 1.6;
  padding: 2rem;
  margin: 0;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Navigation bar */
.md-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.md-navbar-logo {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  text-decoration: none;
}

.md-navbar-logo img {
  height: 2rem;
  margin-right: 0.5rem;
  margin-bottom: 0;
  margin-top: 0;
}

.md-navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.translate-button {
  min-width: 120px;
}

.md-navbar-back {
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.md-navbar-back:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Google Translate customization */
.goog-te-gadget {
  font-family: 'Oswald', sans-serif !important;
  color: white !important;
}

.goog-te-gadget-simple {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  color: white !important;
}

.goog-te-menu-value {
  color: white !important;
  text-decoration: none !important;
}

.goog-te-menu-value span {
  color: white !important;
}

/* Hide Google Translate banner but keep the dropdown */
.skiptranslate.goog-te-banner-frame {
  display: none !important;
}

/* Hide Google automatic translation bar */
.goog-te-banner-frame.skiptranslate {
  display: none !important;
}

/* Hide Google automatic popup/notification */
.goog-te-balloon-frame {
  display: none !important;
}

/* Fix the top padding added by Google Translate */
body {
  top: 0 !important;
}

/* Container for better readability */
.markdown-container {
  max-width: 800px;
  margin: 5rem auto 2rem;
  padding: 2rem;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Oswald', sans-serif;
  font-weight: 700;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

h1 {
  font-size: 2.5rem;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 0.3em;
}

h2 {
  font-size: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.3em;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

p {
  margin-bottom: 1.5em;
}

/* Lists */
ul, ol {
  margin-bottom: 1.5em;
  padding-left: 2em;
}

li {
  margin-bottom: 0.5em;
}

/* Links */
a {
  color: #00796b;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

a:hover {
  border-bottom-color: #00796b;
}

/* Code blocks */
pre {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 1em;
  overflow-x: auto;
  margin-bottom: 1.5em;
}

code {
  font-family: monospace;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.2em 0.4em;
  border-radius: 3px;
}

pre code {
  background-color: transparent;
  padding: 0;
}

/* Blockquotes */
blockquote {
  border-left: 4px solid rgba(255, 255, 255, 0.3);
  padding-left: 1em;
  margin-left: 0;
  font-style: italic;
  color: rgba(255, 255, 255, 0.8);
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1.5em;
}

th, td {
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.5em 1em;
  text-align: left;
}

th {
  background-color: rgba(0, 0, 0, 0.2);
}

tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 1em 0;
}

/* Horizontal rule */
hr {
  border: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
  margin: 2em 0;
}

/* Make text selectable, unlike the rest of the app */
* {
  user-select: text;
  -webkit-user-select: text;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  body {
    padding: 1rem;
  }

  .markdown-container {
    padding: 1rem;
    margin-top: 4.5rem;
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  .md-navbar {
    padding: 0.75rem;
  }

  .md-navbar-logo {
    font-size: 1.25rem;
  }

  .md-navbar-logo img {
    height: 1.5rem;
  }

  .md-navbar-back {
    font-size: 0.875rem;
    padding: 0.4rem 0.8rem;
  }
}

/* Print styles */
@media print {
  body {
    background: none;
    color: black;
    animation: none;
    padding: 0;
    margin: 0;
  }

  .md-navbar {
    display: none;
  }

  .markdown-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
    background: none;
    box-shadow: none;
  }

  a {
    color: #000;
    text-decoration: underline;
  }

  h1, h2, h3, h4, h5, h6 {
    color: #000;
    page-break-after: avoid;
  }

  img {
    max-width: 100% !important;
  }

  pre, blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  @page {
    margin: 2cm;
  }
}
