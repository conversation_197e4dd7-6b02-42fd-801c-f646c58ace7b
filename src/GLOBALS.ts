import { FontStyle } from "types/models.ts";

export const GLOBALS = {
  APP_NAME: "RPG Meet",
  PREFIX: "rpg-meet",
  ID_TAB: "rpg-meet-tab",
  singleThrowDisabledId: "single-throw-disabled",
  buttonClasses:
    `w-full m-auto py-1 text-white rounded-lg transform hover:scale-110 text-center`,
} as const;

export const prefixedId = (id: string): string =>
  `${GLOBALS.PREFIX}-${id}` as const;

export const mapAudio: (audioName: string) => HTMLAudioElement = (
  audioName: string,
): HTMLAudioElement => {
  return new Audio(`public/assets/audio/${audioName}.mp3`);
};
export const AUDIO_GLOBALS = {
  OVER9000: mapAudio("over9000") as HTMLAudioElement,
};
export const DEFAULT_FONT_STYLE: FontStyle = {
  "font-family": `'Oswald'`,
  "font-size": `x-large`,
  "font-weight": `900`,
  "fill": `white`,
  "stroke": `white`,
  "stroke-width": `1px`,
} as const;
export const OVERLAY_TABLE_SETTINGS = {
  rows: {
    defaultValue: 2,
    min: 0,
    max: 10,
  },
  columns: {
    defaultValue: 2,
    min: 0,
    max: 3,
  },
} as const;

export const MAX_OVERLAY_FILE_SIZE_MB = 2;

const currentURL = new URL(globalThis.location.href);
const shouldUseLocalServer =
  currentURL.searchParams.get("useLocalServer") === "true";

export const VIDEOCALL_SERVER_BASE_URL = shouldUseLocalServer
  ? "http://localhost:3000"
  : "https://call.rpgmeet.app";

export const WEBCAM_OVERLAY_LOCALFORAGE_ID = "webcam-overlay-settings";
