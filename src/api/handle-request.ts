import { STATUS_CODE, STATUS_TEXT, StatusCode } from "@std/http/status";
import { ErrorWithCode } from "./error-with-code.ts";
import "@std/dotenv/load";

type MandatoryParameter = {
  name: string;
  location: "query" | "body";
  value?: unknown;
};

type Options = {
  request: Request;
  mandatoryParameters: MandatoryParameter[];
  handler: () => Promise<true>;
  responseData: {
    code: StatusCode;
    message: string;
  };
};
const MANDATORY_ENVIRONMENT_VARIABLES = [
  "GITHUB_TOKEN",
  "GITHUB_OWNER",
  "GITHUB_REPO",
  "GITHUB_WORKFLOW_ID",
  "MAILHOOK_SENDER_EMAILS",
  "SMTP_HOST",
  "SMTP_PORT",
  "SMTP_USERNAME",
  "SMTP_PASSWORD",
  "JWT_SECRET",
];

export const handleRequest = async (options: Options) => {
  const { request, mandatoryParameters, handler, responseData } = options;
  // handle cors preflight request
  if (request.method === "OPTIONS") {
    return new Response("ok", {
      status: STATUS_CODE.OK,
    });
  }
  console.clear();
  try {
    const missingEnvironmentVariables = MANDATORY_ENVIRONMENT_VARIABLES.filter(
      (environmentVariable) => !Deno.env.get(environmentVariable),
    );
    if (missingEnvironmentVariables.length > 0) {
      throw new ErrorWithCode(
        `Missing environment variables: ${
          missingEnvironmentVariables.join(
            ", ",
          )
        }`,
        STATUS_CODE.InternalServerError,
      );
    }
    if (request.method !== "POST") {
      throw new ErrorWithCode(
        STATUS_TEXT[STATUS_CODE.MethodNotAllowed],
        STATUS_CODE.MethodNotAllowed,
      );
    }
    if (!request.body) {
      throw new ErrorWithCode("Missing request body", STATUS_CODE.BadRequest);
    }
    for (const parameter of mandatoryParameters) {
      const paramLocation = request[parameter.location as keyof Request];
      const value = paramLocation
        ?.[parameter.name as keyof typeof paramLocation];
      if (!value) {
        throw new ErrorWithCode(
          `Missing ${parameter.location} parameter: ${parameter.name}`,
          STATUS_CODE.BadRequest,
        );
      }
      if (
        parameter.value && value !== parameter.value
      ) {
        throw new ErrorWithCode(
          `Invalid ${parameter.location} parameter value for: ${parameter.name}`,
          STATUS_CODE.BadRequest,
        );
      }
    }
    const success = await handler();
    if (!success) {
      throw new ErrorWithCode("Failed", STATUS_CODE.InternalServerError);
    }
    console.log(`success!`);
    return Response.json(responseData.message, {
      status: responseData.code,
    });
  } catch (e) {
    console.log(e);
    const errorWithCode = e as ErrorWithCode;
    if (!errorWithCode.code) {
      return Response.json({
        message: "Internal server error, returned error without status code",
      }, {
        status: STATUS_CODE.InternalServerError,
      });
    }
    const errorWithoutCode = e instanceof Error ? e.message : e;
    return Response.json({
      message: errorWithoutCode,
    }, {
      status: errorWithCode.code,
    });
  }
};
