import { create, getNumericDate, verify } from "@wok/djwt";
import "@std/dotenv/load";

export const generateKey = async (): Promise<CryptoKey> => {
  const jwtSecret = Deno.env.get("JWT_SECRET")!;
  const key: CryptoKey = await crypto.subtle.importKey(
    "raw",
    new TextEncoder().encode(jwtSecret),
    { name: "HMAC", hash: "SHA-512" },
    true,
    ["sign", "verify"],
  );
  return key;
};
export const generateToken = async (email: string) => {
  const payload = {
    email,
    exp: getNumericDate(60 * 60), // 1 hour expiration
  };
  const key = await generateKey();
  return await create({ alg: "HS512", typ: "JWT" }, payload, key);
};
export { verify };
