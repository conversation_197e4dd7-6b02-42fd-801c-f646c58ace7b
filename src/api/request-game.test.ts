import { STATUS_CODE } from "@std/http/status";
import { requestGame } from "./request-game.ts";
import { assertEquals } from "@std/assert";

Deno.test("requestGame", async () => {
  const body = {
    name: "asd",
    body: "123",
  };
  const request = new Request("http://localhost:8080", {
    method: "POST",
    body: JSON.stringify(body),
  });
  const response = await requestGame(request);
  const content = await response.text();
  console.log({ content });
  const successful = response.status === STATUS_CODE.Created;
  assertEquals(successful, true);
});
