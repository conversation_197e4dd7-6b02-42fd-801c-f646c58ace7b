import { STATUS_CODE } from "@std/http";
import { ErrorWithCode } from "api/error-with-code.ts";
import "@std/dotenv/load";
import { sendTelegramNotification } from "../scripts/send-telegram-notification.ts";
import { createGithubIssue } from "../scripts/create-github-issue.ts";

const MANDATORY_ENVIRONMENT_VARIABLES = [
  "GITHUB_TOKEN",
  "GITHUB_OWNER",
  "GITHUB_REPO",
  "TELEGRAM_TOKEN",
  "TELEGRAM_CHAT_ID",
];
const missingEnvironmentVariables = MANDATORY_ENVIRONMENT_VARIABLES.filter(
  (environmentVariable) => !Deno.env.get(environmentVariable),
);
if (missingEnvironmentVariables.length > 0) {
  throw new ErrorWithCode(
    `Missing environment variables: ${
      missingEnvironmentVariables.join(
        ", ",
      )
    }`,
    STATUS_CODE.InternalServerError,
  );
}
export const requestGame = async (request: Request) => {
  const data = await request.json().catch(() => {
    return { name: null, body: null };
  });
  const missingData = ["name", "body"].filter((key) => !data[key]);
  if (missingData.length > 0) {
    return new Response(`Missing data: ${missingData.join(",")}`, {
      status: STATUS_CODE.BadRequest,
    });
  }
  const { name, body } = data;
  const issue = await createGithubIssue({ name, body });
  if (issue.status !== STATUS_CODE.Created) {
    console.log(issue);
    throw new ErrorWithCode("Failed", STATUS_CODE.InternalServerError);
  }
  const issueNumber = issue.data.number;
  const issueURL = `https://github.com/marcomow/rpg-meet/issues/${issueNumber}`;
  const issueURLFormatted = `<a href="${issueURL}">#${issueNumber}</a>`;
  const message =
    `<b>New game request: ${name}</b> ${issueURLFormatted}\n\n${body}`;
  const sentMessage = await sendTelegramNotification({ message });
  if (!sentMessage.ok) {
    throw new ErrorWithCode("Failed", STATUS_CODE.InternalServerError);
  }
  return new Response("Successfully received", {
    status: STATUS_CODE.Created,
  });
};
