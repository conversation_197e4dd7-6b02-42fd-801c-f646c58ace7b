import { STATUS_CODE, STATUS_TEXT } from "@std/http/status";
import { status } from "./status.ts";
import { requestGame } from "api/request-game.ts";
import { verifyToken } from "api/verify-token.ts";

type Handler = (request: Request) => Promise<Response>;

const routes: { [key: string]: Handler } = {
  "status": status,
  "request-game": requestGame,
  "verify-token": verifyToken,
} as const;

export const apiRouter = async (request: Request): Promise<Response> => {
  const path = new URL(request.url).pathname;
  const pathStart = path.split("api/")[1] as keyof typeof routes;
  const handler = routes[pathStart];
  if (!handler) {
    const response = new Response(STATUS_TEXT[STATUS_CODE.NotFound], {
      status: STATUS_CODE.NotFound,
    });
    return response;
  }
  return handler(request);
};
