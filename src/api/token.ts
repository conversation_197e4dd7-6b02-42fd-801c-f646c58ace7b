import { validateToken } from "@kinde/jwt-validator";
import { jwtDecoder } from "@kinde/jwt-decoder";
import { init, Users } from "@kinde/management-api-js";
import "@std/dotenv/load";

export const verifyToken = async (request: Request) => {
  const headers = request.headers;
  const token = headers.get("Authorization")?.split(" ")[1];
  if (!token) {
    throw new Error("No token provided");
  }
  const { valid } = await validateToken({
    token,
    domain: Deno.env.get("KINDE_DOMAIN")!,
  });
  console.log({ valid });
  if (!valid) throw new Error("Invalid token");
  const decodedToken = jwtDecoder(token)!;

  init({
    kindeDomain: Deno.env.get("KINDE_DOMAIN")!,
    clientId: Deno.env.get("KINDE_MANAGEMENT_CLIENT_ID")!,
    clientSecret: Deno.env.get("KINDE_MANAGEMENT_CLIENT_SECRET")!,
  });
  const { users } = await Users.getUsers({
    userId: decodedToken.sub,
  });
  return users?.[0];
};

export const getEmailFromToken = async (request: Request) => {
  const decodedToken = await verifyToken(request);
  //@ts-ignore asd
  const email = decodedToken.email;
  if (!email) {
    throw new Error("Invalid token: no email found");
  }
  return email;
};
