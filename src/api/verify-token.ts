import { STATUS_CODE } from "@std/http/status";
import { generateKey, verify } from "api/jwt.ts";
import { kv } from "api/kv.ts";

export const verifyToken = async (request: Request) => {
  const { token } = await request.json();
  if (!token) {
    return new Response("Missing token", {
      status: STATUS_CODE.BadRequest,
    });
  }
  const key = await generateKey();
  const verified = await verify<{
    email: string;
  }>(token, key);
  if (!verified) {
    return new Response("Invalid token", {
      status: STATUS_CODE.Unauthorized,
    });
  }
  const email = verified.email;
  await kv.set(["users", email], {
    lastLogin: new Date(),
  });
  return Response.json({ email }, {
    status: STATUS_CODE.OK,
  });
};
