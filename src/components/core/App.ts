import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Hall } from "./Hall.ts";
import { Singleton } from "./Singleton.ts";
import { Room } from "../room/Room.ts";
import { ShowSidebarButton } from "components/sidebar/ShowSidebarButton.ts";

export class App extends Singleton {
  static override class = [
    "w-full",
    "h-full",
    "grid",
    "gradient-background",
    "justify-center",
  ];

  async connectedCallback() {
    this.showHall();
  }

  showHall() {
    this.innerHTML = `<${Hall.tag}></${Hall.tag}>`;
  }

  reset() {
    history.pushState(null, "", "/");
    this.showHall();
  }

  showRoom(roomName: string) {
    this.innerHTML = `
      <${Room.tag}  
        room-name="${roomName}"
      ></${Room.tag}>
      <${ShowSidebarButton.tag}></${ShowSidebarButton.tag}>
      `;
  }
}
safeCustomDefine(App);
