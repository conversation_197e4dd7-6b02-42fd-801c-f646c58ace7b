import { kebabCase } from "npm:string-ts";
import { prefixedId } from "GLOBALS";
export class GenericComponent extends HTMLElement {
  static get tag() {
    return prefixedId(kebabCase(this.name));
  }
  static class: string[] = [];
  static style: string = "";

  tag = (this.constructor as typeof GenericComponent).tag;

  private staticThis = this.constructor as typeof GenericComponent;

  applyStyleClasses(additionalClass: string[] = []) {
    const styleClasses = this.staticThis.class;
    const allClasses = [...styleClasses, ...additionalClass];
    if (allClasses.length === 0) return;
    this.classList.add(...allClasses);
  }
  private applyStyle() {
    const style = this.staticThis.style;
    if (!style) return;
    this.setAttribute(
      "style",
      [style, this.getAttribute("style")]
        .filter(Boolean)
        .join(";"),
    );
  }
  constructor() {
    super();
    this.applyStyleClasses();
    this.applyStyle();
  }
}
