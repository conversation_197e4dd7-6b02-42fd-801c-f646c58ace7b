import { GLO<PERSON>LS } from "GLOBALS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Logo } from "./Logo.ts";
import { animateCSS } from "utils/animateCss.ts";
import { Login } from "./Login.ts";
import { Information } from "core/Information.ts";
import { RoomSelection } from "components/room/RoomSelection.ts";
import { Singleton } from "core/Singleton.ts";

export class Hall extends Singleton {
  static override class = [
    "w-full",
    "h-full",
    "grid",
    "justify-center",
    "items-center",
  ];
  connectedCallback() {
    this.innerHTML = `
        <div class="grid grid-flow-col gap-10 items-center">
          <span 
            app-name
            class="grid justify-end items-center text-5xl md:text-6xl oswald" 
          >${GLOBALS.APP_NAME}</span>
          <div app-logo class="w-full grid justify-start items-center">
            <${Logo.tag} style="width:10em"></${Logo.tag}>
          </div> 
        </div>
        <div content></div>
        <${Information.tag} class="hidden grid justify-center text-center"></${Information.tag}>`;
    Promise.all([
      animateCSS(
        this.querySelector("[app-name]")!,
        "lightSpeedInLeft",
      ),
      animateCSS(this.querySelector("[app-logo]")!, "lightSpeedInRight"),
    ]).then(async () => {
      this.querySelector(Information.tag)?.classList.remove("hidden");
      await Login.login();
    });
  }
  private static async getContent() {
    return this.getInstance<Hall>().then((hall) =>
      hall.querySelector("[content]")!
    );
  }
  static async showLogin() {
    await this.getContent().then((content) => {
      content.innerHTML = `<${Login.tag}></${Login.tag}>`;
    });
  }
  static async showRoomSelection() {
    await this.getContent().then((content) => {
      content.innerHTML = `<${RoomSelection.tag}></${RoomSelection.tag}>`;
    });
  }
}
safeCustomDefine(Hall);
