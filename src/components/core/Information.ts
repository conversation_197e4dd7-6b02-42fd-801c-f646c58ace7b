import { GenericComponent } from "core/GenericComponent.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { TranslationController } from "controllers/TranslationController.ts";

export class Information extends GenericComponent {
  async connectedCallback() {
    const docs = [
      {
        fileName: "terms-and-conditions",
        translationKey: "termsAndConditions",
      },
      { fileName: "privacy-policy", translationKey: "privacyPolicy" },
      { fileName: "cookie-policy", translationKey: "cookiePolicy" },
      { fileName: "help", translationKey: "help" },
    ];

    // Add the About link to the landing page
    const landingLink = {
      url: "/landing.html",
      translationKey: "about",
    };
    // Generate content for documentation links
    const docContents = docs.map(({ fileName, translationKey }) =>
      `<li>
            <a class="underline" href="/public/docs/${fileName}.md" target="_blank" ${ATTRIBUTE_TRANSLATION_KEY}="${translationKey}"></a>
        </li>`
    ).join("");

    // Generate content for the landing page link
    const landingContent = `<li>
            <a class="underline" href="${landingLink.url}" target="_blank" ${ATTRIBUTE_TRANSLATION_KEY}="${landingLink.translationKey}"></a>
        </li>`;

    // Combine all content
    const contents = docContents + landingContent;

    this.innerHTML = `
      <ul>
        ${contents}
      </ul>`;
    this.querySelectorAll<HTMLAnchorElement>("a").forEach(async (anchor) => {
      anchor.addEventListener("click", async (e) => {
        e.preventDefault();
        const lang = await TranslationController.instance.then(
          async (controller) => await controller.language,
        );
        const url = new URL(anchor.href);

        // Add language parameter to all links
        url.searchParams.set("lang", lang);

        // Open the link in a new tab
        globalThis.open(url.href, "_blank");
      });
    });
  }
}

safeCustomDefine(Information);
