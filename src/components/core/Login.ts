import { GenericComponent } from "./GenericComponent.ts";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "GLOBALS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Loading, Notify } from "notiflix";
import { ATTRIBUTE_TRANSLATION_KEY, TRANSLATIONS } from "TRANSLATIONS";
import { App } from "./App.ts";
import { animateCSS } from "utils/animateCss.ts";
import createKindeClient, {
  KindeClient,
  KindeUser,
} from "@kinde-oss/kinde-auth-pkce-js";
import { devLog } from "utils/devLog.ts";
import { Hall } from "core/Hall.ts";
import { TranslationController } from "controllers/TranslationController.ts";

export class Login extends GenericComponent {
  static override tag = `${GLOBALS.PREFIX}-login`;
  static override class = [
    "w-full",
    "h-full",
    "grid",
    "justify-center",
    "items-center",
  ];
  private static user: KindeUser | null = null;
  private static async getClient(): Promise<KindeClient> {
    //@ts-expect-error wrong types
    const client: KindeClient = await createKindeClient({
      client_id: "66b876b19a6944e791bfcce0284bb0e9",
      domain: "https://auth.rpgmeet.app",
      redirect_uri: location.origin,
      on_redirect_callback: (
        _user: KindeUser,
        appState?: { kindeOriginUrl: string },
      ) => {
        if (appState?.kindeOriginUrl) {
          history.pushState({}, "", appState.kindeOriginUrl);
        }
      },
    });
    return client;
  }

  static get token(): Promise<string> {
    return this.user
      ? this.getClient().then((client) =>
        client.getToken().then((token) => token || "")
      )
      : Promise.resolve("");
  }

  static get email(): Promise<string> {
    const email = this.user?.email;
    if (!email) throw new Error("No email found");
    return Promise.resolve(email);
  }

  static async login() {
    const language = await TranslationController.instance.then(
      async (controller) => await controller.language,
    );
    Loading.dots(TRANSLATIONS.loggingIn.values.text[language]);

    try {
      const client = await this.getClient();
      const isAuthenticated = await client.isAuthenticated();
      devLog({ isAuthenticated });
      if (isAuthenticated) {
        Login.user = client.getUser();
        const email = Login.user.email;

        Notify.info(
          `${TRANSLATIONS.loggedInAs.values.text[language]} ${email}`,
          {
            showOnlyTheLastOne: true,
            timeout: 1000,
            clickToClose: true,
          },
        );
        await Hall.showRoomSelection();
      } else {
        Notify.info(TRANSLATIONS.notLoggedIn.values.text[language], {
          showOnlyTheLastOne: true,
        });
        await Hall.showLogin();
      }
    } catch (error) {
      console.error("Error checking authentication:", error);
      Notify.failure("Error checking login status");
    } finally {
      Loading.remove();
    }
  }

  static async logout() {
    const client = await this.getClient();
    await client.logout();
    Login.user = null;
    const language = await TranslationController.instance.then(
      async (controller) => await controller.language,
    );
    Notify.info(TRANSLATIONS.loggedOut.values.text[language], {
      showOnlyTheLastOne: true,
    });
    App.getInstance<App>().then((app) => app.reset());
  }

  async connectedCallback() {
    this.innerHTML = `
      <div class="grid gap-4"> 
        <button id="login-button" class="text-2xl py-2 px-4 rounded ${GLOBALS.buttonClasses} bg-red-900" ${ATTRIBUTE_TRANSLATION_KEY}="login">
        </button>
      </div>
    `;

    const loginButton = this.querySelector("#login-button");
    if (loginButton) {
      loginButton.addEventListener("click", async () => {
        const client = await Login.getClient();
        await client.login();
      });
    }

    await animateCSS(this, "zoomIn");
  }
}

safeCustomDefine(Login);
