import { GenericComponent } from "./GenericComponent.ts";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "GLO<PERSON>LS";
import { Confirm } from "notiflix";
import { TranslationController } from "controllers/TranslationController.ts";
import { ATTRIBUTE_TRANSLATION_KEY, TRANSLATIONS } from "TRANSLATIONS";
import { Login } from "core/Login.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";

export class Logout extends GenericComponent {
  async connectedCallback() {
    this.innerHTML = `
    <div>
        <p ${ATTRIBUTE_TRANSLATION_KEY}="currentUser"></p> 
        <p>${await Login
      .email}</p>
    </div>
    <button ${ATTRIBUTE_TRANSLATION_KEY}="logout" class="${GLOBALS.buttonClasses} bg-red-600"></button>
      `;
    this.querySelector("button")?.addEventListener("click", async () => {
      const translationController = await TranslationController.instance;
      const language = await translationController.language;
      Confirm.show(
        TRANSLATIONS.logout.values.text[language],
        "",
        TRANSLATIONS.confirm.values.text[language],
        TRANSLATIONS.cancel.values.text[language],
        async () => await Login.logout(),
        () => {},
        {
          okButtonBackground: "#dc2626",
          cancelButtonBackground: "#3085d6",
        },
      );
    });
  }
}
safeCustomDefine(Logout);
