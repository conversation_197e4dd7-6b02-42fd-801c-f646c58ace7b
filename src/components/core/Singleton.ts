import { GenericComponent } from "./GenericComponent.ts";

export class <PERSON>ton extends GenericComponent {
  static async getInstance<T extends Singleton>(): Promise<T> {
    return new Promise((resolve) => {
      const interval = setInterval(() => {
        const instance = document.querySelector(this.tag) as T;
        if (instance) {
          clearInterval(interval);
          resolve(instance);
        }
      }, 100);
    });
  }
}
