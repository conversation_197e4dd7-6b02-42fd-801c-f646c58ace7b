import { store } from "store";
import { mapAudio } from "GLOBALS";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { GeneratedPicTile } from "types/models.ts";
import {
  ResultDescription,
  ResultPic,
  ResultValue,
  TextResultInput,
} from "types/results.ts";
import { animateCSS } from "utils/animateCss.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { GeneratedPicDie } from "../dice/die/GeneratedPicDie.ts";
import { DieClassName } from "../dice/helpers/diceClassesIndex.ts";
import { InputMultidice } from "../dice/helpers/InputMultidice.ts";
import { DeckStatus } from "./DeckStatus.ts";

const suits = ["♥️", "♣️", "♦️", "♠️"] as const;
const suitsLiteral = ["hearts", "clubs", "diamonds", "spades"] as const;
const getSuitsLiteral = (suit: typeof suits[number]) => {
  const suitIndex = suits.findIndex((s: string): boolean => {
    return s.charCodeAt(0) === suit.charCodeAt(0);
  });
  const suitLiteral = suitsLiteral[suitIndex];
  return suitLiteral;
};
const flush = ["A", "K", "Q", "J", 10, 9, 8, 7, 6, 5, 4, 3, 2];
const baseDeck = [
  // '🃏', '🃏',
  ...suits
    .map((suit: string): string[] =>
      flush.map((card: string | number): string => suit + card)
    )
    .flat(),
];
const idLocalforage = `deck-cards`;
const audioShuffle = mapAudio("shuffle-all");

export class Deck extends GeneratedPicDie {
  override containerAnimations: string[] = ["animate__slideInUp"];
  override dieAnimations: string[] = [
    "animate__slideInDown",
    "animate__faster",
  ];
  override overlayAnimations: string[] = ["slideInUp"];

  static async getCurrentDeck(): Promise<string[]> {
    return await store.getItem(idLocalforage) || baseDeck;
  }
  static async updateDeck(sides: string[]) {
    if (sides.length === 0) {
      await this.resetDeck();
      return;
    }
    await store.setItem(idLocalforage, sides);
    const deck = document.querySelector<Deck>(Deck.tag);
    if (deck) {
      const inputMultidice = deck.querySelector(
        InputMultidice.tag,
      ) as InputMultidice;
      if (inputMultidice) {
        inputMultidice.max = sides.length;
      }
    }
    const deckStatus = document.querySelector(DeckStatus.tag) as DeckStatus;
    if (deckStatus) deckStatus.cardsAmount = sides.length;
  }
  static async resetDeck() {
    await this.updateDeck(baseDeck);
    audioShuffle.play();
    const deckButton = document.querySelector(Deck.tag)?.querySelector(
      "button",
    ) as HTMLButtonElement;
    if (deckButton) {
      animateCSS(deckButton, "headShake");
    }
  }
  override get instanceClassName(): DieClassName {
    return "Deck";
  }
  override get resultAudios() {
    return ["deal-end"];
  }
  override get sides(): string[] {
    return baseDeck;
  }
  override get throwAudios() {
    return ["deal1", "deal2", "deal3"];
  }
  override get tile(): GeneratedPicTile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/deck`,
      fileName: `RPGMeet_card.svg`,
      templateName: `RPGMeet_card-SUIT.svg`,
    };
  }
  protected override generateResultDescription(
    textResultInput: TextResultInput,
  ): Promise<ResultDescription> {
    const dieDisplayName = `
             <span ${ATTRIBUTE_TRANSLATION_KEY}="card${
      textResultInput.values.length > 1 ? "s" : ""
    }"></span>
             <span ${ATTRIBUTE_TRANSLATION_KEY}="from the deck"></span>`;
    return super.generateResultDescription({
      ...textResultInput,
      dieDisplayName,
    });
  }
  protected override async generateResultPics(
    textResultInput: TextResultInput,
  ): Promise<ResultPic[]> {
    const values = textResultInput.values as ResultValue[];
    const picsPromises = values.map(
      async (value: ResultValue): Promise<ResultPic> => {
        const suit = value.slice(0, 1) as typeof suits[number];
        const suitLiteral = getSuitsLiteral(suit);
        const isRedSuit = suitLiteral === "hearts" ||
          suitLiteral === "diamonds";
        const suitColor = isRedSuit ? "#9e3030" : "#444";
        const flush = value.slice(1);
        const path = `${this.tile.imgPath}/${this.tile.templateName}`.replace(
          "SUIT",
          suitLiteral,
        );
        const templateString = await (await fetch(path)).text();
        const resultTextInverted = `${flush}${suit}`;
        const renderedImage = templateString
          .replace(
            new RegExp("<svg>"),
            `<svg class="w-14 filter drop-shadow-lg">`,
          )
          .replace(
            new RegExp("</svg>"),
            `
                    <text 
                        x="49%" y="48%" 
                        text-anchor="middle" 
                        alignment-baseline="central" 
                        style="font-family:'Oswald';font-size:x-large;font-weight:900;fill:${suitColor};stroke:${suitColor};stroke-width:1px;"
                    >${resultTextInverted}</text></svg>`,
          );

        return renderedImage;
      },
    );
    const pics = await Promise.all(picsPromises);
    return pics;
  }
  protected override async generateResultValues(
    rolls: number,
  ): Promise<ResultValue[]> {
    const cardsInDeck: string[] = await Deck.getCurrentDeck();
    const deck = cardsInDeck
      .map((a: string): [number, string] => [Math.random(), a])
      .sort((a: [number, string], b: [number, string]): number => a[0] - b[0])
      .map((a: [number, string]): string => a[1]);
    const extractedCards = deck.splice(0, rolls);
    await Deck.updateDeck(deck);
    const sortedCards = extractedCards
      .sort((a: string, b: string): number => {
        const suitA = a.slice(0, 1) as typeof suits[number];
        const suitB = b.slice(0, 1) as typeof suits[number];
        const suitLiteralA = getSuitsLiteral(suitA);
        const suitLiteralB = getSuitsLiteral(suitB);
        const suitLiteralAIndex = suitsLiteral.indexOf(suitLiteralA);
        const suitLiteralBIndex = suitsLiteral.indexOf(suitLiteralB);
        if (suitLiteralAIndex < suitLiteralBIndex) {
          return -1;
        }
        const flushA = a.slice(1) as typeof flush[number];
        const flushB = b.slice(1) as typeof flush[number];
        const flushAIndex = flush.indexOf(flushA);
        const flushBIndex = flush.indexOf(flushB);
        return flushAIndex - flushBIndex;
      });
    const results = sortedCards;
    return results;
  }
}
safeCustomDefine(Deck);
