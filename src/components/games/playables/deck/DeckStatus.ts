import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Deck } from "./Deck.ts";
import { GenericComponent } from "../../../core/GenericComponent.ts";

export class DeckStatus extends GenericComponent {
  set cardsAmount(amount: number) {
    (this.querySelector("[deck-cards-amount]") as HTMLSpanElement).innerText =
      amount.toString();
  }
  async connectedCallback() {
    this.innerHTML = `
            <span ${ATTRIBUTE_TRANSLATION_KEY}="Cards in the deck"></span>
            <span deck-cards-amount class="font-bold"></span>
        `;
    this.cardsAmount = (await Deck.getCurrentDeck()).length;
  }
}
safeCustomDefine(DeckStatus);
