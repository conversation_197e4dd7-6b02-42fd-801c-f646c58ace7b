import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Deck } from "./Deck.ts";
import { GenericComponent } from "../../../core/GenericComponent.ts";

export class ResetDeck extends GenericComponent {
  async connectedCallback() {
    this.innerHTML = `
            <button 
              class="border px-2 py-1 rounded transform hover:bg-blue-600 hover:text-white hover:scale-110"
              ${ATTRIBUTE_TRANSLATION_KEY}="shuffle deck"
            ></button>
        `;
    this.querySelector("button")!.addEventListener("click", async () => {
      await Deck.resetDeck();
    });
  }
}
safeCustomDefine(ResetDeck);
