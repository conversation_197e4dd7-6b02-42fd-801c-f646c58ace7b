import { TranslationController } from "controllers/TranslationController.ts";
import { AUDIO_G<PERSON><PERSON><PERSON><PERSON>, G<PERSON><PERSON><PERSON><PERSON>, mapAudio } from "GL<PERSON><PERSON><PERSON>";
import { ATTRIBUTE_TRANSLATION_KEY, TRANSLATIONS } from "TRANSLATIONS";
import { Tile } from "types/models.ts";
import {
  DisplayResultInput,
  PlayAudioOptions,
  Result,
  ResultDescription,
  ResultValue,
  TextResultInput,
} from "types/results.ts";
import { devLog } from "utils/devLog.ts";
import { SwitchHideResult } from "utils/SwitchHideResult.ts";
import { vibrateMultipleDice, vibrateSingleDie } from "utils/vibration.ts";
import { MessagesSection } from "../../../sidebar/MessagesSection.ts";
import { DiceGroupRollButton } from "../../sets/DiceGroupRollButton.ts";
import { DieClassName } from "./helpers/diceClassesIndex.ts";
import { DieGroupRollCheckbox } from "./helpers/DieGroupRollCheckbox.ts";
import { InputMultidice } from "./helpers/InputMultidice.ts";
import { GenericComponent } from "../../../core/GenericComponent.ts";
import { MessageDetails } from "types/communication.ts";
import { Videocall } from "../../../room/Videocall.ts";
import { Notify } from "notiflix";

export class Die extends GenericComponent {
  private _isRollable: boolean = true;
  containerAnimations: string[] = ["animate__heartBeat"];
  dieAnimations: string[] = ["animate__flip", "animate__faster"];
  overlayAnimations: DisplayResultInput["overlayAnimations"];

  get dieGroupCheckbox(): DieGroupRollCheckbox | undefined {
    return this.querySelector(DieGroupRollCheckbox.tag) as DieGroupRollCheckbox;
  }
  get dieName(): string {
    const dieName = this.getAttribute("die-name");
    if (!dieName) {
      throw 'missing attribute "die-name"';
    }
    return dieName;
  }
  get dieSize(): number {
    const dieSize = this.getAttribute("die-size");
    if (!dieSize) throw 'missing attribute "die-size"';
    const dieSizeNumber: number = Number(dieSize);
    if (!dieSizeNumber) throw `die-size is not a number`;
    return dieSizeNumber;
  }
  get hasSum(): boolean {
    return this.getAttribute("has-sum") === "true" || false;
  }
  get htmlButton(): Promise<string> {
    const tile = this.tile;
    const tileURL = `${tile.imgPath}/${tile.fileName}`;
    const singleThrowAnimation =
      this.hasAttribute(GLOBALS.singleThrowDisabledId as string)
        ? ""
        : "cursor-pointer transform hover:scale-125";
    const labelText = this.getAttribute("label-text") || "";
    const labelPosition = this.getAttribute("label-position") || "top";
    const anchorName = `--${crypto.randomUUID()}`;
    const labelHtml = `
      <label
        class="m-1"
        style="position:absolute;position-anchor: ${anchorName};position-area: ${labelPosition};"
        ${ATTRIBUTE_TRANSLATION_KEY}="${labelText}"
      >
      </label>`;
    const htmlButton = `
        <button
            class="mx-auto ${singleThrowAnimation}"
            title="${this.dieName}"
            style="height: ${tile.cssSize}; width: ${tile.cssSize};anchor-name: ${anchorName}"
        >
            <div class="filter drop-shadow-lg">
              <img src="${tileURL}">
            </div>
        </button>
        ${labelText ? labelHtml : ""}
    `;
    return Promise.resolve(htmlButton);
  }
  get instanceClassName(): DieClassName | "die" {
    return `die`;
  }
  get isRollable(): boolean {
    const isSetRollable = this._isRollable;
    if (!isSetRollable) {
      return false;
    }
    if (this.isSingle) {
      return true;
    }
    const isMultidiceInputValid =
      (this.querySelector(InputMultidice.tag) as InputMultidice).isValid;
    return isMultidiceInputValid;
  }
  set isRollable(isRollable) {
    this._isRollable = isRollable;
  }
  get isSingle(): boolean {
    return this.hasAttribute("single-die");
  }
  get mod(): string | null {
    return this.getAttribute("mod");
  }
  set mod(mod: string | null) {
    if (mod) {
      this.setAttribute("mod", mod);
    } else {
      this.removeAttribute("mod");
    }
  }
  get resultAudios(): string[] {
    return ["die2", "die1", "die3"];
  }
  get rolls(): number {
    return !this.isSingle
      ? (this.querySelector(InputMultidice.tag) as InputMultidice).value
      : 1;
  }
  get sides(): (string | number)[] {
    return Array.from({ length: this.dieSize })
      .map((_a: unknown, index: number) => index + 1);
  }
  get sortResults(): "ascending" | "descending" {
    return "descending";
  }
  get throwAudios(): string[] {
    return ["throw1", "throw2", "throw3", "throw4", "throw5"];
  }
  get tile(): Tile {
    return {
      imgPath: `public/assets/img`,
      cssSize: `48px`,
      fileName: `logo.svg`,
    };
  }
  async connectedCallback() {
    const span = this.getAttribute("col-span") || "1";
    this.classList.add("m-auto", `col-span-${span}`);
    const min = this.getAttribute("min") || "1";
    const max = this.getAttribute("max") || "20";
    const step = this.getAttribute("step") || "1";
    const diceGroupRollOptional = this.hasAttribute(
      `${DiceGroupRollButton.tag}-optional`,
    );
    const inputMultidiceHTML = `
            <${InputMultidice.tag}
                die="${this.dieSize}"
                die-name="${this.dieName}"
                class="m-auto ${this.isSingle ? "hidden" : ""}"
                ${this.isSingle ? 'single-die="true"' : ""}
                min="${min}" max="${max}" step="${step}"
            >
            </${InputMultidice.tag}>`;

    const dieGroupRollCheckboxHTML =
      this.getAttribute(DiceGroupRollButton.tag) && diceGroupRollOptional
        ? `<${DieGroupRollCheckbox.tag}></${DieGroupRollCheckbox.tag}>`
        : "";

    this.innerHTML = `
            <div class="p-1 grid" title="${this.dieName}">
                ${await this.htmlButton}
                ${inputMultidiceHTML}
                ${dieGroupRollCheckboxHTML}
            </div>
        `;
    const singleThrowDisabled = this.hasAttribute(
      GLOBALS.singleThrowDisabledId,
    );
    const button = this.querySelector<HTMLButtonElement>("button");
    if (!singleThrowDisabled) {
      button?.addEventListener("click", () => this.roll());
    }
    this.addEventListener("animationend", () => {
      this.isRollable = true;
      button?.classList.remove("animate__animated");
      this.querySelector("svg")?.classList.remove("animate__animated");
    });
  }
  async generateResult(): Promise<Result> {
    const values: ResultValue[] = await this.generateResultValues(this.rolls);
    const textResultInput: TextResultInput = {
      values,
      dieName: this.dieName,
      dieDisplayName: this.dieName,
    };
    const pics = await this.generateResultPics(textResultInput);
    const description: ResultDescription = await this.generateResultDescription(
      textResultInput,
    );
    const result: Result = {
      values,
      pics,
      description,
      dieName: this.dieName,
      dieDisplayName: this.dieName,
    };
    return result;
  }
  protected generateResultDescription(
    textResultInput: TextResultInput,
  ) {
    const numberResults = textResultInput.values.length;
    const dieDisplayName = textResultInput.dieDisplayName ||
      textResultInput.dieName || this.dieName;
    if (this.hasSum) {
      const sum = textResultInput.values
        .reduce(
          (a: number | string, b: number | string): number =>
            Number(a) + Number(b),
          0,
        ) as number;
      const displaySum = this.sumFunction(sum);
      return Promise.resolve(
        `<b>${numberResults}${dieDisplayName} (${displaySum})</b>`,
      );
    }
    return Promise.resolve(`<b>${numberResults}${dieDisplayName}</b>`);
  }
  protected async generateResultPics(
    textResultInput: TextResultInput,
  ): Promise<ResultValue[]> {
    const stringPromises = textResultInput.values
      .map((value: ResultValue): ResultValue => {
        const valueURL = `${this.tile.imgPath}/${value}`;
        return `<img src="${valueURL}" alt="${value}" />`;
      });
    return stringPromises;
  }
  protected generateResultValues(rolls: number): Promise<ResultValue[]> {
    const results: string[] = Array.from({ length: rolls })
      .map(() => Math.floor(Math.random() * this.dieSize))
      .sort((a: number, b: number): number =>
        this.sortResults === "descending" ? b - a : a - b
      )
      .map((index: number): string => this.sides[index].toString());
    return Promise.resolve(results);
  }
  async roll() {
    if (this.rolls < 1) {
      const translationController = await TranslationController.instance;
      const language = await translationController.language;
      const oneRollWarning =
        TRANSLATIONS["oneRollWarning"].values.text[language];
      Notify.warning(oneRollWarning);
      return;
    }
    if (!this.isRollable) return;
    this.isRollable = false;

    // Add vibration feedback for dice throw
    if (this.rolls === 1) {
      vibrateSingleDie();
    } else {
      vibrateMultipleDice();
    }

    this.querySelector("button")?.classList.add(
      "animate__animated",
      ...this.containerAnimations,
    );
    this.querySelector("svg")?.classList.add(
      "animate__animated",
      ...this.dieAnimations,
    );

    const result: Result = await this.generateResult();
    devLog({ result }, { location: "console" });
    const resultText: string = MessagesSection.formatDiceRoll(result);
    const timestamp: number = Date.now();
    const hidingResults: boolean = SwitchHideResult.active;
    const messagesSection = document.querySelector<MessagesSection>(
      MessagesSection.tag,
    )!;
    const objectMessage: MessageDetails = {
      from: "You",
      type: "die",
      timestamp,
      text: resultText +
        (hidingResults
          ? `<span ${ATTRIBUTE_TRANSLATION_KEY}="hidden to others"><span>`
          : ""),
    };
    devLog({
      throwAudios: this.throwAudios,
    }, { location: "console" });
    await Die.playAudioThrow({
      throwAudios: this.throwAudios,
      resultAudios: this.resultAudios,
      values: result.values,
    });
    this.isRollable = true;
    await messagesSection.add(objectMessage);
    const messageWithAudio: MessageDetails = {
      ...objectMessage,
      audio: {
        resultAudios: this.resultAudios,
        values: result.values,
        throwAudios: this.throwAudios,
      },
    };
    await Videocall.getInstance<Videocall>().then(async (videocall) =>
      await videocall.sendMessage(messageWithAudio)
    );
  }
  static async playAudioThrow(playAudioOptions: PlayAudioOptions) {
    devLog(playAudioOptions, { location: "console" });
    const throwAudio: HTMLAudioElement = mapAudio(
      playAudioOptions
        .throwAudios[
          Math.floor(Math.random() * playAudioOptions.throwAudios.length)
        ],
    );
    await throwAudio.play();

    const maxSounds = 3;
    const rollAudios = Array.from({
      length: playAudioOptions.values.length > maxSounds
        ? maxSounds
        : playAudioOptions.values.length,
    })
      .map((): HTMLAudioElement =>
        mapAudio(
          playAudioOptions
            .resultAudios[
              Math.floor(Math.random() * playAudioOptions.resultAudios.length)
            ],
        )
      );
    await throwAudio.play();
    const timeout = setTimeout(() => {
      rollAudios.forEach((audio: HTMLAudioElement, index: number) => {
        const interval = setInterval(() => {
          audio.play();
          clearInterval(interval);
        }, index * 100);
      });
      clearTimeout(timeout);
    }, 500);

    // Add vibration feedback when dice results are announced
    if (playAudioOptions.values.length === 1) {
      vibrateSingleDie();
    } else {
      vibrateMultipleDice();
    }
    devLog(playAudioOptions.values, { location: "console" });
    if (
      playAudioOptions.values.find((value: string | number): boolean =>
        Number(value) === 33
      ) && playAudioOptions.values.length == 1
    ) {
      devLog("big");
      if (globalThis.speechSynthesis) {
        const textSpeech: SpeechSynthesisUtterance =
          new SpeechSynthesisUtterance(`oh my god...it's so big!`);
        globalThis.speechSynthesis.speak(textSpeech);
      }
    }
    if (playAudioOptions.values.find((value) => Number(value) > 9000)) {
      AUDIO_GLOBALS.OVER9000.play();
    }
  }
  protected sumFunction(sum: number): number | string {
    return sum;
  }
}
