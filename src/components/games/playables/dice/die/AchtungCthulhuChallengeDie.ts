import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class AchtungCthulhuChallengeDie extends Die {
  override get instanceClassName(): DieClassName {
    return "AchtungCthulhuChallengeDie";
  }
  override get sides(): string[] {
    return [
      "RPGMeet_Achtung-challenge-1.svg",
      "RPGMeet_Achtung-challenge-2.svg",
      "RPGMeet_Achtung-challenge-3-4.svg",
      "RPGMeet_Achtung-challenge-3-4.svg",
      "RPGMeet_Achtung-challenge-5-6.svg",
      "RPGMeet_Achtung-challenge-5-6.svg",
    ];
  }
  override get sortResults(): "ascending" | "descending" {
    return "ascending";
  }
  override get tile(): Tile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/achtung-cthulhu`,
      fileName: `RPGMeet_Achtung-challenge-1.svg`,
    };
  }
}
safeCustomDefine(AchtungCthulhuChallengeDie);
