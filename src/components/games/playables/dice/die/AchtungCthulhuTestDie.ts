import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { FontStyle, GeneratedPicTile } from "types/models.ts";
import { TextResultInput } from "types/results.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { GeneratedPicDie } from "./GeneratedPicDie.ts";

export class AchtungCthulhuTestDie extends GeneratedPicDie {
  public override get fontStyle(): FontStyle {
    return {
      ...super.fontStyle,
      "font-family": `'Bokor', cursive`,
      "font-weight": `normal`,
      stroke: "none",
    };
  }
  override get generatedValuesExceptions(): {
    values: string[];
    templateName: string;
    size?: number;
  }[] {
    return [
      {
        values: ["10"],
        templateName: `RPGMeet_Achtung-test-10.svg`,
      },
      {
        values: ["20"],
        templateName: `RPGMeet_Achtung-test.svg`,
      },
    ];
  }
  override get instanceClassName(): DieClassName {
    return "AchtungCthulhuTestDie";
  }
  override get sortResults(): "ascending" | "descending" {
    return "ascending";
  }
  override get tile(): GeneratedPicTile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/achtung-cthulhu`,
      fileName: `RPGMeet_Achtung-test.svg`,
      templateName: `RPGMeet_Achtung-test-empty.svg`,
    };
  }
  override async generateResultDescription(
    textResultInput: TextResultInput,
  ): Promise<string> {
    const customDieName: string =
      `<span ${ATTRIBUTE_TRANSLATION_KEY}="${this.dieName}"></span>`;
    return await super.generateResultDescription({
      ...textResultInput,
      dieDisplayName: customDieName,
    });
  }
}
safeCustomDefine(AchtungCthulhuTestDie);
