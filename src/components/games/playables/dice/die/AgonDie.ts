import { GeneratedPicTile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { GeneratedPicDie } from "./GeneratedPicDie.ts";

export class AgonDie extends GeneratedPicDie {
  override get generatedValuesExceptions(): {
    values: string[];
    templateName: string;
    size?: number;
  }[] {
    return [
      {
        values: ["6"],
        size: 6,
        templateName: `RPGMeet_Agon-${this.dieName}-d${this.dieSize}.svg`,
      },
    ];
  }
  override get instanceClassName(): DieClassName {
    return "AgonDie";
  }
  override get tile(): GeneratedPicTile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/agon`,
      fileName: `RPGMeet_Agon-${this.dieName}-d${this.dieSize}.svg`,
      templateName: `RPGMeet_Agon-${this.dieName}-d${this.dieSize}-empty.svg`,
    };
  }
}
safeCustomDefine(AgonDie);
