import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { FlavoredDie } from "dice/die/FlavoredDie.ts";
import { ResultValue, TextResultInput } from "types/results.ts";

export class AvatarDie extends FlavoredDie {
  override get instanceClassName(): DieClassName {
    return "AvatarDie";
  }
  static override get flavors() {
    const flavors = ["air", "earth", "fire", "water", "tech", "weapon"];
    const flavorsMap = flavors.reduce((acc, flavor) => {
      acc[flavor] = Array.from({ length: 6 }).map((_, i) =>
        `RPGMeet_Avatar-${flavor}-${i + 1}.svg`
      );
      return acc;
    }, {} as Record<string, string[]>);
    return flavorsMap;
  }
  override get tile(): Tile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/avatar`,
      fileName: `RPGMeet_Avatar-${this.flavor}-6.svg`,
    };
  }
  protected override async generateResultPics(
    textResultInput: TextResultInput,
  ): Promise<ResultValue[]> {
    const stringPromises = textResultInput.values
      .map((value: ResultValue): ResultValue => {
        const valueURL =
          `${this.tile.imgPath}/RPGMeet_Avatar-${this.flavor}-${value}.svg`;
        return `<img src="${valueURL}" alt="${value}" />`;
      });
    return stringPromises;
  }
}
safeCustomDefine(AvatarDie);
