import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { Tile } from "types/models.ts";
import { TextResultInput } from "types/results.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class BrokenCompassDie extends Die {
  override get instanceClassName(): DieClassName {
    return "BrokenCompassDie";
  }
  override get sides(): string[] {
    return [
      "RPGMeet_BC-yellow-compass.svg",
      "RPGMeet_BC-yellow-north.svg",
      "RPGMeet_BC-yellow-west.svg",
      "RPGMeet_BC-yellow-south.svg",
      "RPGMeet_BC-yellow-east.svg",
      "RPGMeet_BC-yellow-skull.svg",
    ];
  }
  override get resultAudios() {
    const audios: string[] = ["die2", "die1", "die3"];
    return audios;
  }
  override get tile(): Tile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/bc`,
      fileName: `RPGMeet_BC-yellow-compass.svg`,
    };
  }
  protected override generateResultDescription(
    textResultInput: TextResultInput,
  ) {
    return Promise.resolve(`
        <b>
            ${
      textResultInput.values.length > 1
        ? `${textResultInput.values.length} <span ${ATTRIBUTE_TRANSLATION_KEY}="compass dice"></span>`
        : `<span ${ATTRIBUTE_TRANSLATION_KEY}="one compass die"></span>`
    }
        </b>`);
  }
}
safeCustomDefine(BrokenCompassDie);
