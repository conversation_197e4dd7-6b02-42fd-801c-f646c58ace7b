import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class BrokenCompassLuckCoin extends Die {
  override get instanceClassName(): DieClassName {
    return "BrokenCompassLuckCoin";
  }
  override get sides(): string[] {
    return [
      "RPGMeet_BC-luckycoin-compass.svg",
      "RPGMeet_BC-luckycoin-skull.svg",
    ];
  }
  override get resultAudios() {
    const audios = this.rolls > 1
      ? ["coin_drop_single1", "coin_drop_single2"]
      : ["coin_drop_multiple1", "coin_drop_multiple2"];
    return audios;
  }
  override get tile(): Tile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/bc`,
      fileName: `RPGMeet_BC-luckycoin.svg`,
    };
  }
  protected override generateResultDescription() {
    return Promise.resolve(
      `<span ${ATTRIBUTE_TRANSLATION_KEY}="luck coin"></span>`,
    );
  }
}
safeCustomDefine(BrokenCompassLuckCoin);
