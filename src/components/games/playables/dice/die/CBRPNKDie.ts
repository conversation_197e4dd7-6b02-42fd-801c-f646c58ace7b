import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class CBRPNKDie extends Die {
  override get instanceClassName(): DieClassName {
    return "CBRPNKDie";
  }

  override get sortResults(): "ascending" | "descending" {
    return "descending";
  }

  override get sides(): string[] {
    return Array.from({ length: 6 }).map((_, i) =>
      `RPGMeet_CBR-PNK-${i + 1}.svg`
    );
  }

  override get throwAudios() {
    return ["cbrpnk1", "cbrpnk2", "cbrpnk3", "cbrpnk4", "cbrpnk5", "cbrpnk6"];
  }

  override get resultAudios() {
    return ["cbrpnk1", "cbrpnk2", "cbrpnk3", "cbrpnk4", "cbrpnk5", "cbrpnk6"];
  }

  override get tile(): Tile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/cbrpnk`,
      fileName: "RPGMeet_CBR-PNK-tile.svg",
    };
  }
}

safeCustomDefine(CBRPNKDie);
