import { FontStyle, GeneratedPicTile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { GeneratedPicDie } from "./GeneratedPicDie.ts";

export class CityOfMistDie extends GeneratedPicDie {
  override get generatedValuesExceptions(): {
    values: string[];
    templateName: string;
  }[] {
    return [
      { values: ["6"], templateName: `RPGMeet_CoM-${this.dieName}-6.svg` },
    ];
  }
  override get fontStyle(): FontStyle {
    return {
      ...super.fontStyle,
      fill: this.dieName === "logos" ? "#630060" : "white",
      stroke: this.dieName === "logos" ? "#630060" : "white",
    };
  }
  override get instanceClassName(): DieClassName {
    return "CityOfMistDie";
  }
  override get tile(): GeneratedPicTile {
    const tileFileName: Record<typeof this.dieName, string> = {
      "logos": "RPGMeet_CoM-logos-6.svg",
      "mythos": "RPGMeet_CoM-mythos-6.svg",
    };
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/city-of-mist`,
      fileName: tileFileName[this.dieName],
      templateName: `RPGMeet_CoM-${this.dieName}-1-5.svg`,
    };
  }
}
safeCustomDefine(CityOfMistDie);
