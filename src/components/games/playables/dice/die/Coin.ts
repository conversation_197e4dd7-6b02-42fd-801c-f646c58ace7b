import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { Tile } from "types/models.ts";
import { TextResultInput } from "types/results.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class Coin extends Die {
  override get instanceClassName(): DieClassName {
    return "Coin";
  }
  override get sides(): string[] {
    return ["RPGMeet_coin-heads.svg", "RPGMeet_coin-tails.svg"];
  }
  override get tile(): Tile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/poly`,
      fileName: "RPGMeet_poly-d2.svg",
    };
  }
  override get resultAudios() {
    const audios: string[] = ["coin_drop_multiple1", "coin_drop_multiple2"];
    return audios;
  }
  protected override generateResultDescription(
    textResultInput: TextResultInput,
  ) {
    const numberResults = textResultInput.values.length;
    return Promise.resolve(`
        <b>
            ${numberResults} <span ${ATTRIBUTE_TRANSLATION_KEY}="coin${
      textResultInput.values.length > 1 ? "s" : ""
    }"></span>
        </b>`);
  }
}
safeCustomDefine(Coin);
