import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { GeneratedPicTile } from "types/models.ts";
import { TextResultInput } from "types/results.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { GeneratedPicDie } from "./GeneratedPicDie.ts";

export class CthulhuDarkDie extends GeneratedPicDie {
  override get instanceClassName(): DieClassName {
    return "CthulhuDarkDie";
  }
  override get tile(): GeneratedPicTile {
    const name = this.dieName === "insight" ? "insight" : "normal";
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/cthulhu-dark`,
      fileName: `RPGMeet_CthulhuDark-${name}.svg`,
      templateName: `RPGMeet_CthulhuDark-${name}.svg`,
    };
  }
  override async generateResultDescription(
    textResultInput: TextResultInput,
  ): Promise<string> {
    const customDieName: string =
      `<span ${ATTRIBUTE_TRANSLATION_KEY}="${this.dieName}"></span>`;
    return await super.generateResultDescription({
      ...textResultInput,
      dieDisplayName: customDieName,
    });
  }
}
safeCustomDefine(CthulhuDarkDie);
