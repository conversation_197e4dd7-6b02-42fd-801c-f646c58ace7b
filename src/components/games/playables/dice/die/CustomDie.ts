import { store } from "store";

import { GeneratedPicTile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { InputMultidice } from "../helpers/InputMultidice.ts";
import { PolyDie } from "./PolyhedricDie.ts";

export class CustomDie extends PolyDie {
  override get dieName(): string {
    return this.dieSize ? `d${this.dieSize}` : "custom die";
  }
  override get dieSize(): number {
    return Number(
      (this.querySelector<HTMLInputElement>("#custom_die_size"))?.value,
    );
  }
  override get htmlButton(): Promise<string> {
    return new Promise((resolve) => {
      const defaultValue = 3;
      const filePath = `${this.tile.imgPath}/${this.tile.fileName}`;
      fetch(filePath)
        .then((res) => res.text())
        .then((tilePic) => {
          tilePic = tilePic
            .replace(
              `<svg`,
              `<svg height="${this.tile.cssSize}" width="${this.tile.cssSize}" class="${
                this.dieAnimations.join(" ")
              }"`,
            );
          const buttonText = this.getAttribute("button-text");
          if (buttonText) {
            tilePic = tilePic
              .replace(
                new RegExp("</svg>"),
                `
                            <text x="49%" y="48%" 
                                text-anchor="middle" 
                                alignment-baseline="central" style="${
                  this.getAttribute("button-text-font-string")
                }"
                            >${buttonText}</text></svg>`,
              );
          }
          const htmlButton = `
                <div class="flex gap-1">
                    <style>
                        #custom_die_button:hover:after{
                            margin-top:10px;
                            font-size: small;
                        }
                    </style>
                    <button id="custom_die_button" class="" title="${this.dieName}">
                        ${tilePic}
                    </button>
                    <input 
                        id="custom_die_size" 
                        type="number" 
                        min="3" max="999" step="1" 
                        class="w-8 my-auto" 
                        required
                        value="${defaultValue}"
                    >
                </div>
            `;
          resolve(htmlButton);
        });
    });
  }
  override get instanceClassName(): DieClassName {
    return "CustomDie";
  }
  override get isRollable(): boolean {
    return this.isValid && super["isRollable"];
  }
  override set isRollable(isThrowable) {
    super["isRollable"] = isThrowable;
  }
  get isValid(): boolean {
    const dieSizeInput = this.querySelector<HTMLInputElement>(
      "#custom_die_size",
    );
    if (!dieSizeInput) return false;
    dieSizeInput.setCustomValidity("");
    const isValid: boolean = dieSizeInput?.checkValidity();
    if (!isValid || !dieSizeInput.value) {
      dieSizeInput.setCustomValidity("min 3");
      dieSizeInput.reportValidity();
      return false;
    }
    return true;
  }
  override get tile(): GeneratedPicTile {
    return {
      ...super.tile,
      fileName: `RPGMeet_poly-dx.svg`,
      templateName: `RPGMeet_poly-empty-dx.svg`,
    };
  }
  override async connectedCallback() {
    await super.connectedCallback();
    const value = await store.getItem(this.dieName);
    const customDieSizeElement = this.querySelector<HTMLInputElement>(
      "#custom_die_size",
    )!;
    customDieSizeElement.value = String(value ?? 3);
    this.querySelector<InputMultidice>(InputMultidice.tag)?.setAttribute(
      "die",
      "custom",
    );
    customDieSizeElement.addEventListener("change", () => {
      if (this.isValid) store.setItem(this.dieName, this.dieSize);
    });
  }
}
safeCustomDefine(CustomDie);
