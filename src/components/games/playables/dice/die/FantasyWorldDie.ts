import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class FantasyWorldDie extends Die {
  override get instanceClassName(): DieClassName {
    return "FantasyWorldDie";
  }
  override get sides(): string[] {
    const sideNames = [1, 2, 3, 4, 5, 6];
    return sideNames.map((sideName) => `RPGMeet_FantasyWorld-${sideName}.svg`);
  }
  override get tile(): Tile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/fantasy-world`,
      fileName: `RPGMeet_FantasyWorld-6.svg`,
    };
  }
}
safeCustomDefine(FantasyWorldDie);
