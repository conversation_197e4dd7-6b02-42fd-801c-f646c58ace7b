import { Die } from "dice/Die.ts";
import { devLog } from "utils/devLog.ts";
import { ResultValue, TextResultInput } from "types/results.ts";

type Flavor = string;

export class FlavoredDie extends Die {
  static flavors: {
    [key: Flavor]: string[];
  } = {};
  get flavor(): Flavor {
    const attribute = this.getAttribute("flavor");
    if (attribute) return attribute;
    const constructor = this.constructor as typeof FlavoredDie;
    devLog({
      message: `${constructor.tag} has no flavor attribute, using first flavor`,
      element: this,
    });
    const firstFlavor = Object.keys(constructor.flavors)[0];
    if (firstFlavor) return firstFlavor;
    devLog({
      message: `${constructor.tag} has no flavors`,
      element: this,
    });
    throw new Error("No flavors specified");
  }
  protected override async generateResultPics(
    textResultInput: TextResultInput,
  ): Promise<ResultValue[]> {
    const stringPromises = textResultInput.values
      .map((value: ResultValue): ResultValue => {
        const valueURL = `${this.tile.imgPath}${value}`;
        return `<img src="${valueURL}" alt="${value}" />`;
      });
    return stringPromises;
  }
}
