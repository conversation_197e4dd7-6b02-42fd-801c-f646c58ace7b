import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class FudgeDie extends Die {
  override get instanceClassName(): DieClassName {
    return "FudgeDie";
  }
  override get sortResults(): "ascending" | "descending" {
    return "ascending";
  }
  override get sides(): string[] {
    return [
      "RPGMeet_fudge-1-plus.svg",
      "RPGMeet_fudge-1-plus.svg",
      "RPGMeet_fudge-2-minus.svg",
      "RPGMeet_fudge-2-minus.svg",
      "RPGMeet_fudge-3-empty.svg",
      "RPGMeet_fudge-3-empty.svg",
    ];
  }
  override get tile(): Tile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/fudge`,
      fileName: "RPGMeet_fudge.svg",
    };
  }
}
safeCustomDefine(FudgeDie);
