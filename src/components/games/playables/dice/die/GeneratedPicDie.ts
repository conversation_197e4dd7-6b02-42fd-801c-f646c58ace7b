import { FontStyle, GeneratedPicTile } from "types/models.ts";
import { ResultValue, TextResultInput } from "types/results.ts";
import { Die } from "../Die.ts";

export class GeneratedPicDie extends Die {
  svgTemplateString: string | undefined;

  public get fontStyle(): FontStyle {
    return {
      "font-family": `'Oswald'`,
      "font-size": `x-large`,
      "font-weight": `900`,
      fill: `white`,
      stroke: `white`,
      "stroke-width": `1px`,
    };
  }
  override get sides(): (number | string)[] {
    return Array.from({ length: this.dieSize })
      .map((_, i) => Number(i) + 1);
  }
  override get tile(): GeneratedPicTile {
    return {
      ...super.tile,
      templateName: `logo.svg`,
    };
  }
  get generatedValuesExceptions(): {
    values: string[];
    templateName: string;
    size?: number;
  }[] {
    return [];
  }
  async getTemplatePic(templateName?: string): Promise<string> {
    const tile = this.tile;
    const templatePath = `${tile.imgPath}/${templateName || tile.templateName}`;
    const fullPath = `${templatePath}`;
    const templatePic = await fetch(fullPath).then((r) => r.text());
    return templatePic;
  }
  protected override async generateResultPics(
    textResultInput: TextResultInput,
  ): Promise<ResultValue[]> {
    const templatePic = await this.getTemplatePic();
    const exceptionPics = await Promise.all(
      this.generatedValuesExceptions.map(
        async ({ values, templateName: templatePicPath, size }) => {
          return {
            values,
            size,
            pic: await this.getTemplatePic(templatePicPath),
          };
        },
      ),
    );
    const pics = textResultInput.values
      .map((value: ResultValue): ResultValue => {
        const exceptionPic = exceptionPics.find(
          ({ values: exceptionValues, size }) => {
            if (!exceptionValues.includes(value)) {
              return false;
            }
            if (!!size && size !== this.dieSize) {
              return false;
            }
            return true;
          },
        );
        if (exceptionPic) {
          return exceptionPic.pic;
        }
        const fontString = Object.keys(this.fontStyle)
          .map((key) => `${key}: ${this.fontStyle[key as keyof FontStyle]}`)
          .join(";");
        return templatePic
          .replace(
            new RegExp("<svg>"),
            `<svg class="w-14 filter drop-shadow-lg">`,
          )
          .replace(
            new RegExp("</svg>"),
            `
                        <text x="49%" y="48%" 
                            text-anchor="middle" 
                            alignment-baseline="central" style="${fontString}"
                        >${value}</text></svg>`,
          );
      });
    return pics;
  }
}
