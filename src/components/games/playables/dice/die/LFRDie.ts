import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { ContainerDiceIcon, Tile } from "types/models.ts";
import { TextResultInput } from "types/results.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class LFRDie extends Die {
  override get instanceClassName(): DieClassName {
    return "LFRDie";
  }
  override get sides(): string[] {
    const sidesMap: ContainerDiceIcon = {
      6: [
        "RPGMeet_l5a-ring-01-str-exp",
        "RPGMeet_l5a-ring-02-succ",
        "RPGMeet_l5a-ring-03-str-succ",
        "RPGMeet_l5a-ring-04-opp",
        "RPGMeet_l5a-ring-05-str-opp",
        "RPGMeet_l5a-ring-06-null",
      ],
      12: [
        "RPGMeet_l5a-skill-01-exp",
        "RPGMeet_l5a-skill-02-str-exp-31",
        "RPGMeet_l5a-skill-03-opp-succ",
        "RPGMeet_l5a-skill-04-succ",
        "RPGMeet_l5a-skill-04-succ",
        "RPGMeet_l5a-skill-05-str-succ",
        "RPGMeet_l5a-skill-05-str-succ",
        "RPGMeet_l5a-skill-06-str-exp-30",
        "RPGMeet_l5a-skill-06-str-exp-30",
        "RPGMeet_l5a-skill-06-str-exp-30",
        "RPGMeet_l5a-skill-07-null",
        "RPGMeet_l5a-skill-07-null",
      ],
    };
    return (sidesMap[this.dieSize] as string[]).map((fileName) =>
      `${fileName}.svg`
    );
  }
  override get tile(): Tile {
    const fileName: Record<typeof this.dieSize, string> = {
      6: "RPGMeet_l5a-ring-01-str-exp",
      12: "RPGMeet_l5a-skill-01-exp",
    };
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/lfr`,
      fileName: `${fileName[this.dieSize]}.svg`,
    };
  }
  override async generateResultDescription(
    textResultInput: TextResultInput,
  ): Promise<string> {
    const customDieName: string = `<span ${ATTRIBUTE_TRANSLATION_KEY}="${
      this.dieSize === 6 ? "ring" : "skill"
    }"></span>`;
    return await super.generateResultDescription({
      ...textResultInput,
      dieDisplayName: customDieName,
    });
  }
}
safeCustomDefine(LFRDie);
