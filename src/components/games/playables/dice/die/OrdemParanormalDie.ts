import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { ContainerDiceIcon, Tile } from "types/models.ts";
import { TextResultInput } from "types/results.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class OrdemParanormalDie extends Die {
  override get instanceClassName(): DieClassName {
    return "OrdemParanormalDie";
  }
  override get sides(): string[] {
    const fillArray: (length: number, filler: string) => string[] = (
      length: number,
      filler: string,
    ): string[] => Array.from({ length }).fill(filler) as string[];
    
    const sidesMap: ContainerDiceIcon = {
      "d4": fillArray(4, "RPGMeet_ordem-paranormal-d4"),
      "d6": fillArray(6, "RPGMeet_ordem-paranormal-d6"),
      "d8": fillArray(8, "RPGMeet_ordem-paranormal-d8"),
      "d10": fillArray(10, "RPGMeet_ordem-paranormal-d10"),
      "d12": fillArray(12, "RPGMeet_ordem-paranormal-d12"),
      "d20": fillArray(20, "RPGMeet_ordem-paranormal-d20"),
    };
    
    return (sidesMap[this.dieName] as string[]).map((
      fileName: string,
    ): string => `${fileName}.svg`);
  }
  
  override get tile(): Tile {
    const fileName: Record<typeof this.dieName, string> = {
      "d4": "RPGMeet_ordem-paranormal-d4",
      "d6": "RPGMeet_ordem-paranormal-d6", 
      "d8": "RPGMeet_ordem-paranormal-d8",
      "d10": "RPGMeet_ordem-paranormal-d10",
      "d12": "RPGMeet_ordem-paranormal-d12",
      "d20": "RPGMeet_ordem-paranormal-d20",
    };
    
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/ordem-paranormal`,
      fileName: `${fileName[this.dieName]}.svg`,
    };
  }
  
  override async generateResultDescription(
    textResultInput: TextResultInput,
  ): Promise<string> {
    const customDieName: string = `<span ${ATTRIBUTE_TRANSLATION_KEY}="${this.dieName}"></span>`;
    return await super.generateResultDescription({
      ...textResultInput,
      dieDisplayName: customDieName,
    });
  }
}
safeCustomDefine(OrdemParanormalDie);
