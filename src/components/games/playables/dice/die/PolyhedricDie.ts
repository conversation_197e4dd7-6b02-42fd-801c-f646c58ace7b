import { DEFAULT_FONT_STYLE } from "GLOBALS";
import { FontStyle, GeneratedPicTile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { GeneratedPicDie } from "./GeneratedPicDie.ts";

export class PolyDie extends GeneratedPicDie {
  override get instanceClassName(): DieClassName {
    return "PolyDie";
  }
  public override get fontStyle(): FontStyle {
    const color = this.getAttribute("color");
    return {
      ...DEFAULT_FONT_STYLE,
      fill: color === "white" ? "#454545" : "white",
      stroke: color === "white" ? "#454545" : "white",
    };
  }
  override get tile(): GeneratedPicTile {
    const colorAttribute = this.getAttribute("color");
    const color: string = colorAttribute ? `-${colorAttribute}` : "";
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/poly`,
      fileName: `RPGMeet_poly-empty-d${this.dieSize}${color}.svg`,
      templateName: `RPGMeet_poly-empty-d${this.dieSize}${color}.svg`,
    };
  }
}
safeCustomDefine(PolyDie);
