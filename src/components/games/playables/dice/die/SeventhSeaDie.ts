import { FontStyle, GeneratedPicTile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { GeneratedPicDie } from "./GeneratedPicDie.ts";

export class SeventhSeaDie extends GeneratedPicDie {
  public override get fontStyle(): FontStyle {
    const isHS = this.mod === "HS";
    return {
      ...super.fontStyle,
      "font-family": `'Volkhov', serif`,
      "font-weight": `normal`,
      "font-size": `1.2em`,
      fill: isHS ? "white" : `black`,
      stroke: isHS ? "white" : `black`,
    };
  }
  override get generatedValuesExceptions(): {
    values: string[];
    templateName: string;
    size?: number;
  }[] {
    const HS = this.mod === "HS" ? "-HS" : "";
    return [
      {
        values: ["1"],
        templateName: `RPGMeet_7thS${HS}-skull.svg`,
      },
    ];
  }
  override get instanceClassName(): DieClassName {
    return "SeventhSeaDie";
  }
  override get tile(): GeneratedPicTile {
    const HS = this.mod === "HS" ? "-HS" : "";
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/seventh-sea`,
      fileName: `RPGMeet_7thS${HS}-skull.svg`,
      templateName: `RPGMeet_7thS${HS}-empty.svg`,
    };
  }
  protected override sumFunction(sum: number): string | number {
    const incrementsBy10 = Math.floor(sum / 10);
    const incrementsBy15 = Math.floor(sum / 15);
    return `10→${incrementsBy10} | 15→${incrementsBy15}`;
  }
}
safeCustomDefine(SeventhSeaDie);
