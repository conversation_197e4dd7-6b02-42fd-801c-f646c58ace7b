import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class SixBulletsDie extends Die {
  override get instanceClassName(): DieClassName {
    return "SixBulletsDie";
  }
  override get sortResults(): "ascending" | "descending" {
    return "ascending";
  }
  override get sides(): string[] {
    return Array.from({ length: 6 }).map((_, i) =>
      `dadi-RPG-Meet-DEF_6BS-${i + 1}.svg`
    );
  }
  override get throwAudios() {
    return ["die-six-bullets-1", "die-six-bullets-2"];
  }
  override get tile(): Tile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/six-bullets`,
      fileName: "dadi-RPG-Meet-DEF_6BS-6.svg",
    };
  }
}
safeCustomDefine(SixBulletsDie);
