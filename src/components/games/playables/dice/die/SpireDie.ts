import { GeneratedPicTile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { PolyDie } from "./PolyhedricDie.ts";

export class SpireDie extends PolyDie {
  override get tile(): GeneratedPicTile {
    return {
      ...super.tile,
      imgPath: `public/assets/img/spire`,
      fileName: `RPGMeet_Spire-d${this.dieSize}.svg`,
      templateName: `RPGMeet_Spire-d${this.dieSize}-empty.svg`,
    };
  }
}
safeCustomDefine(SpireDie);
