import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { ContainerDiceIcon, Tile } from "types/models.ts";
import { TextResultInput } from "types/results.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class ValravenDie extends Die {
  override get instanceClassName(): DieClassName {
    return "ValravenDie";
  }
  override get sides(): string[] {
    const fillArray: (length: number, filler: string) => string[] = (
      length: number,
      filler: string,
    ): string[] => Array.from({ length }).fill(filler) as string[];
    const sidesMap: ContainerDiceIcon = {
      "normal": [
        "RPGMeet_Valraven-1",
        ...fillArray(5, "RPGMeet_Valraven-2-3-4-5-6"),
      ],
      "advantage": [
        "RPGMeet_Valraven-ADV-1",
        ...fillArray(5, "RPGMeet_Valraven-ADV-2-3-4-5-6"),
      ],
      "disadvantage": [
        "RPGMeet_Valraven-DIS-1-2",
        ...fillArray(5, "RPGMeet_Valraven-DIS-3-4-5-6"),
      ],
    };
    return (sidesMap[this.dieName] as string[])
      .map((fileName: string): string => `${fileName}.svg`);
  }
  override get tile(): Tile {
    const fileName: Record<typeof this.dieName, string> = {
      "normal": "RPGMeet_Valraven-1",
      "advantage": "RPGMeet_Valraven-ADV-1",
      "disadvantage": "RPGMeet_Valraven-DIS-1-2",
    };
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/valraven`,
      fileName: `${fileName[this.dieName]}.svg`,
    };
  }
  override async generateResultDescription(
    textResultInput: TextResultInput,
  ): Promise<string> {
    const customDieName: string =
      `<span ${ATTRIBUTE_TRANSLATION_KEY}="${this.dieName}"></span>`;
    return await super.generateResultDescription({
      ...textResultInput,
      dieDisplayName: customDieName,
    });
  }
}
safeCustomDefine(ValravenDie);
