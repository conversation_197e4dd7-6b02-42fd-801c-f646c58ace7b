import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { ContainerDiceIcon, Tile } from "types/models.ts";
import { TextResultInput } from "types/results.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export class VampireDie extends Die {
  override get instanceClassName(): DieClassName {
    return "VampireDie";
  }
  override get sides(): string[] {
    const fillArray: (length: number, filler: string) => string[] = (
      length: number,
      filler: string,
    ): string[] => Array.from({ length }).fill(filler) as string[];
    const sidesMap: ContainerDiceIcon = {
      "normal": [
        "RPGMeet_vamp5-black-01-crit",
        ...fillArray(4, "RPGMeet_vamp5-black-02-succ"),
        ...fillArray(5, "RPGMeet_vamp5-black-03-null"),
      ],
      "hunger": [
        "RPGMeet_vamp5-red-01-crit",
        ...fillArray(4, "RPGMeet_vamp5-red-02-succ"),
        ...fillArray(4, "RPGMeet_vamp5-red-04-null"),
        "RPGMeet_vamp5-red-03-fail",
      ],
    };
    return (sidesMap[this.dieName] as string[]).map((
      fileName: string,
    ): string => `${fileName}.svg`);
  }
  override get tile(): Tile {
    const fileName: Record<typeof this.dieName, string> = {
      "normal": "RPGMeet_vamp5-black-01-crit",
      "hunger": "RPGMeet_vamp5-red-01-crit",
    };
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/vampire`,
      fileName: `${fileName[this.dieName]}.svg`,
    };
  }
  override async generateResultDescription(
    textResultInput: TextResultInput,
  ): Promise<string> {
    const customDieName: string = `<span ${ATTRIBUTE_TRANSLATION_KEY}="${
      this.dieName === "normal" ? "vampire" : "hunger"
    }"></span>`;
    return await super.generateResultDescription({
      ...textResultInput,
      dieDisplayName: customDieName,
    });
  }
}
safeCustomDefine(VampireDie);
