import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { GeneratedPicTile } from "types/models.ts";
import { TextResultInput } from "types/results.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { GeneratedPicDie } from "./GeneratedPicDie.ts";

export class VampireNumberDie extends GeneratedPicDie {
  override get dieName(): string {
    return "resonance";
  }
  override get instanceClassName(): DieClassName {
    return "VampireNumberDie";
  }
  override get isSingle(): boolean {
    return true;
  }
  override get tile(): GeneratedPicTile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/vampire`,
      fileName: `RPGMeet_vamp5-black-numbers.svg`,
      templateName: `RPGMeet_vamp5-black-03-null.svg`,
    };
  }
  override async generateResultDescription(
    textResultInput: TextResultInput,
  ): Promise<string> {
    const customDieName: string =
      `<span ${ATTRIBUTE_TRANSLATION_KEY}="resonance"></span>`;
    return await super.generateResultDescription({
      ...textResultInput,
      dieDisplayName: customDieName,
    });
  }
}
safeCustomDefine(VampireNumberDie);
