import { FontStyle, GeneratedPicTile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { GeneratedPicDie } from "./GeneratedPicDie.ts";

export class VampireV20Die extends GeneratedPicDie {
  public override get fontStyle(): FontStyle {
    return {
      ...super.fontStyle,
      "font-family": `'Volkhov', serif`,
      "font-weight": `normal`,
      "font-size": `1.2em`,
      fill: `#ff1a1a`,
      stroke: "#ff1a1a",
    };
  }
  override get generatedValuesExceptions(): {
    values: string[];
    templateName: string;
    size?: number;
  }[] {
    return [
      {
        values: ["1"],
        templateName: `RPGMeet_V20-ankh.svg`,
      },
    ];
  }
  override get instanceClassName(): DieClassName {
    return "VampireV20Die";
  }
  override get tile(): GeneratedPicTile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/vampire-v20`,
      fileName: `RPGMeet_V20-ankh.svg`,
      templateName: `RPGMeet_V20-empty.svg`,
    };
  }
}
safeCustomDefine(VampireV20Die);
