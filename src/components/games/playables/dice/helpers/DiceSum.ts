import { store } from "store";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DiceSumSwitch } from "./DiceSumSwitch.ts";
import { GeneratedPicDie } from "dice/die/GeneratedPicDie.ts";

export class DiceSum extends GeneratedPicDie {
  static get showingAttribute(): string {
    return "showing";
  }
  static get observedAttributes(): string[] {
    return [DiceSum.showingAttribute];
  }
  override async connectedCallback() {
    const sumString = this.getAttribute("value");
    const sum: number = Number(sumString);
    if (!isNaN(sum)) {
      this.innerHTML = ` = ${sumString}`;
    }
    this.setAttribute(
      DiceSum.showingAttribute,
      (await store.getItem(DiceSumSwitch.idLocalForage))!,
    );
  }
  attributeChangedCallback(
    name: string,
    _oldValue: string,
    newValue: string,
  ): void {
    if (name === DiceSum.showingAttribute) {
      this.classList[newValue + "" === "true" ? "remove" : "add"]("hidden");
    }
  }
}
safeCustomDefine(DiceSum);
