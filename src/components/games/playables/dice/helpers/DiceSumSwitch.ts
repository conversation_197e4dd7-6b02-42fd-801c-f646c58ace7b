import { store } from "store";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DiceSum } from "./DiceSum.ts";
import { GenericComponent } from "../../../../core/GenericComponent.ts";

export class DiceSumSwitch extends GenericComponent {
  static get idLocalForage() {
    return `${DiceSumSwitch.tag}-active`;
  }
  async connectedCallback() {
    this.innerHTML = `<div> 
            <input class="cursor-pointer place-self-center" id="dice-sum-input" type="checkbox" ${
      await store.getItem(DiceSumSwitch.idLocalForage) ? "checked" : ""
    }>
            <label class="cursor-pointer text-center" for="dice-sum-input" ${ATTRIBUTE_TRANSLATION_KEY}="SHOW SUMS"></label>
        </div>`;
    this.querySelector("input")!.addEventListener(
      "change",
      (event: Event) => {
        const input: HTMLInputElement = event.target as HTMLInputElement;
        const isChecked: boolean = input.checked;
        store.setItem(DiceSumSwitch.idLocalForage, isChecked);
        document.querySelectorAll<DiceSum>(DiceSum.tag).forEach(
          (diceSum) => {
            diceSum[isChecked ? "setAttribute" : "removeAttribute"](
              DiceSum.showingAttribute,
              "true",
            );
          },
        );
      },
    );
  }
}
safeCustomDefine(DiceSumSwitch);
