import { store } from "store";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { GenericComponent } from "../../../../core/GenericComponent.ts";

export class DieGroupRollCheckbox extends GenericComponent {
  get checked(): boolean {
    const checkbox = this.querySelector(
      'input[type="checkbox"]',
    ) as HTMLInputElement;
    return checkbox?.checked ?? false;
  }
  get die() {
    const findParentInstanceOf = (element: HTMLElement) => {
      if (element instanceof Die) {
        return element;
      }
      if (element.parentElement) {
        return findParentInstanceOf(element.parentElement);
      }
      return undefined;
    };
    const die = findParentInstanceOf(this);
    if (!die) throw new Error("DieGroupRollCheckbox must be inside a Die");
    return die;
  }
  async connectedCallback() {
    this.classList.add("flex", "pt-4");
    const idLocalforage = `${
      (this.constructor as typeof DieGroupRollCheckbox)["tag"]
    }-${this.die.dieName}`;
    const wasChecked = await store.getItem(idLocalforage);
    this.innerHTML = `
            <input type="checkbox" class="m-auto cursor-pointer transform hover:scale-110" ${
      wasChecked ? "checked" : ""
    }>
        `;
    const checkbox = this.querySelector(
      'input[type="checkbox"]',
    ) as HTMLInputElement;
    checkbox.addEventListener("change", () => {
      store.setItem(idLocalforage, this.checked);
    });
  }
}
safeCustomDefine(DieGroupRollCheckbox);
