import { store } from "store";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { GenericComponent } from "../../../../core/GenericComponent.ts";

const maxIdLocalforage = "multi-dice-max";

export class InputMultidice extends GenericComponent {
  valueDieId: string | undefined;

  get value(): number {
    return Number(this.input.value);
  }
  set value(value: number) {
    if (this.isValid) store.setItem(this.valueDieId!, value);
  }
  set max(value: number) {
    this.input.max = value.toString();
    store.setItem(`${maxIdLocalforage}-${this.referenceDieName}`, value);
    this.checkValidity();
  }
  get max(): number {
    return Number(this.input.max);
  }
  get isValid(): boolean {
    return this.checkValidity();
  }
  get referenceDie(): Die {
    const referenceDie = this.closest(`[die-name]`) as Die;
    if (!referenceDie) {
      throw new Error(`${InputMultidice.tag} must be inside a [die-name]`);
    }
    return referenceDie;
  }
  get referenceDieName(): string {
    const dieName = this.referenceDie?.getAttribute("die-name");
    if (!dieName) {
      throw new Error(`missing attribute "die-name" for ${InputMultidice.tag}`);
    }
    return dieName;
  }
  private get input(): HTMLInputElement {
    return this.querySelector("input") as HTMLInputElement;
  }
  async connectedCallback() {
    const isSingle = this.hasAttribute("single-die");
    if (isSingle) {
      return;
    }
    this.valueDieId = `${InputMultidice.tag}-die-${this.getAttribute("die")}-${
      this.getAttribute("die-name")
    }`;
    const min = this.getAttribute("min") || "1";
    const max = await store.getItem(
      `${maxIdLocalforage}-${this.referenceDieName}`,
    ) || this.getAttribute("max") || 20;
    const step = this.getAttribute("step") || "1";
    const value = isSingle ? 1 : (await store.getItem(this.valueDieId) ?? min);
    this.innerHTML = `
            <div class="w-full mt-2 flex">
                <input 
                    class="${isSingle ? "hidden" : ""} w-full mx-2 text-center"
                    type="number" 
                    min="${min}" max="${max}" step="${step}" 
                    value="${value}" required>
            </div>
        `;
    const input: HTMLInputElement = this.input;
    input.addEventListener("change", () => {
      this.value = Number(input.value);
      this.referenceDie.isRollable = this.isValid;
    });
    this.show();
  }
  show(): void {
    const shouldShow = true; //document.querySelector(SwitchMultidice.tag).active;
    this.classList[shouldShow ? "remove" : "add"]("hidden");
  }
  private checkValidity(): boolean {
    const input: HTMLInputElement = this.input;
    input.setCustomValidity("");
    const isValid: boolean = input.checkValidity();
    if (!isValid || !input.value) {
      input.setCustomValidity(`min ${this.input.min}, max ${this.max}`);
      input.reportValidity();
      return false;
    }
    return true;
  }
}
safeCustomDefine(InputMultidice);
