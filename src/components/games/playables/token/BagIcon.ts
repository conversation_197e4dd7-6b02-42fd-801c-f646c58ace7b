import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NotTheEndSet } from "sets/NotTheEndSet.ts";
import { GenericComponent } from "../../../core/GenericComponent.ts";

const htmlTemplate: (isFull: boolean) => string = (isFull: boolean): string => {
  const src = `public/assets/img/token/RPGMeet_NtE-bag_${
    isFull ? "full" : "empty"
  }.svg`;
  const result = `<img src="${src}" alt="bag" />`;
  return result;
};
const animationClasses: string[] = "animate__animated animate__rubberBand"
  .split(" ");
let isFirstRun: boolean = true;

export class BagIcon extends GenericComponent {
  set full(isFull: boolean) {
    this.innerHTML = htmlTemplate(isFull);
    this.title = `the bag is ${isFull ? "full" : "empty"}`;
    if (!isFirstRun) {
      this.classList.add(...animationClasses);
    }
    isFirstRun = false;
  }
  connectedCallback(): void {
    this.addEventListener("animationend", () => {
      this.classList.remove(...animationClasses);
    });
    const sectionToken: NotTheEndSet = this.closest(
      NotTheEndSet.tag,
    ) as NotTheEndSet;
    sectionToken.switchSetup();
  }
}
safeCustomDefine(BagIcon);
