import { store } from "store";
import { TokenColor } from "types/NotTheEnd.ts";
import { NotTheEndSet } from "sets/NotTheEndSet.ts";
import { InputDraw } from "./inputs/InputDraw.ts";
import { InputGeneric } from "./inputs/InputGeneric.ts";
import { InputToken } from "./inputs/InputToken.ts";

const bagContentLocalforageId = "token-bag_content";
const canRiskLocalforageId = "token-can_risk";
const drawResultLocalforageId = "token-draw_result";

export class TokenBag {
  _content: TokenColor[] = [];
  _drawResult: TokenColor[] = [];
  _canRisk: boolean = false;

  get content(): TokenColor[] {
    return this._content;
  }
  set content(content: TokenColor[]) {
    this._content = content;
    store.setItem(bagContentLocalforageId, content);
  }
  get canRisk(): boolean {
    return this._canRisk;
  }
  set canRisk(canRisk: boolean) {
    this._canRisk = canRisk;
    store.setItem(canRiskLocalforageId, canRisk);
  }
  get drawResult(): TokenColor[] {
    return this._drawResult;
  }
  set drawResult(drawResult: TokenColor[]) {
    this._drawResult = drawResult;
    store.setItem(drawResultLocalforageId, drawResult);
  }
  constructor() {
    const persistenceSettings = {
      _content: bagContentLocalforageId,
      _canRisk: canRiskLocalforageId,
      _drawResult: drawResultLocalforageId,
    };
    const setPersisted = (id: keyof typeof persistenceSettings): void => {
      store.getItem(persistenceSettings[id])
        .then(
          (persistentValue: unknown) => {
            if (persistentValue) {
              if (id === "_canRisk") {
                this._canRisk = persistentValue as boolean;
                return;
              }
              this[id] = persistentValue as TokenColor[];
            }
          },
        );
    };
    for (const id in persistenceSettings) {
      setPersisted(id as keyof typeof persistenceSettings);
    }
  }
  fill(): boolean {
    const sectionToken: NotTheEndSet = document.querySelector(
      NotTheEndSet.tag,
    ) as NotTheEndSet;
    const inputs: InputToken[] = sectionToken.tokenInputs;
    const isInputsValid: boolean = [
      ...inputs,
      sectionToken.querySelector<InputToken>(InputDraw.tag),
    ].every((input): boolean => input!.isValid);
    if (!isInputsValid) {
      return false;
    }
    const content: TokenColor[] = [];
    inputs.forEach((input: InputToken): void => {
      content.push(...input.tokens);
    });
    this.content = content;
    return true;
  }
  pick(picks: number, isRisk: boolean = false): TokenColor[] {
    const isFilledCorrectly: boolean = this.content.length > 0 || this.fill();
    const result: TokenColor[] = [];
    if (isFilledCorrectly) {
      const sectionToken: NotTheEndSet = document.querySelector(
        NotTheEndSet.tag,
      ) as NotTheEndSet;
      const originalPicks: number = picks;
      const content = [...this.content];
      while (picks > 0) {
        const token: TokenColor =
          content[Math.floor(Math.random() * content.length)];
        content.splice(content.indexOf(token), 1);
        result.push(token);
        picks--;
      }
      this.content = content;
      this.canRisk = originalPicks < 5 && content.length > 0 &&
        sectionToken.totalTokens > 4;
      if (this.canRisk && !isRisk) {
        this.drawResult = result;
      }
      sectionToken.switchSetup();
    }
    return result;
  }
  empty(): void {
    this.content = [];
    this.canRisk = false;
    const sectionToken: NotTheEndSet = document.querySelector(
      NotTheEndSet.tag,
    ) as NotTheEndSet;
    [
      ...sectionToken.tokenInputs,
      sectionToken.querySelector(InputDraw.tag) as InputDraw,
    ].forEach((input: InputGeneric): void => input.empty());
    sectionToken.switchSetup();
  }
}
export const tokenBag: TokenBag = new TokenBag();
