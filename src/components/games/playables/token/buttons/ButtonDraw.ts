import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NotTheEndSet } from "../../../sets/NotTheEndSet.ts";
import { InputDraw } from "../inputs/InputDraw.ts";
import { InputToken } from "../inputs/InputToken.ts";
import { ButtonPick } from "./ButtonPick.ts";

export class ButtonDraw extends ButtonPick {
  static override get action(): string {
    return "draw";
  }
  get picks(): number {
    const sectionToken = this.closest<NotTheEndSet>(NotTheEndSet.tag)!;
    const picks: number =
      (sectionToken.querySelector<InputToken>(InputDraw.tag)!).value;
    return picks;
  }
}
safeCustomDefine(ButtonDraw);
