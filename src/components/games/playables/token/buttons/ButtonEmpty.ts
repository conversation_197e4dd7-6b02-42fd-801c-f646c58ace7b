import { mapAudio } from "GL<PERSON><PERSON><PERSON>";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { tokenBag } from "../TokenBag.ts";
import { ButtonGeneric } from "./ButtonGeneric.ts";

export class ButtonEmpty extends ButtonGeneric {
  connectedCallback(): void {
    this.innerHTML = `
            <button class="text-yellow-500 bg-gray-700 hover:bg-purple-800 hover:scale-110 p-2 rounded transform uppercase" ${ATTRIBUTE_TRANSLATION_KEY}="empty bag"></button> 
        `;
    this.querySelector("button")!.addEventListener("click", (): void => {
      tokenBag.empty();
      const emptyAudio: HTMLAudioElement = ["bag_empty"].map(mapAudio)[0];
      emptyAudio.play();
    });
  }
}
safeCustomDefine(ButtonEmpty);
