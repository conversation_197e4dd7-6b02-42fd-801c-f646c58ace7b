import { mapAudio } from "GLOBALS";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { TokenColor } from "types/NotTheEnd.ts";
import { SwitchHideResult } from "utils/SwitchHideResult.ts";
import { MessagesSection } from "../../../../sidebar/MessagesSection.ts";
import { NotTheEndSet } from "../../../sets/NotTheEndSet.ts";
import { InputToken } from "../inputs/InputToken.ts";
import { tokenBag } from "../TokenBag.ts";
import { ButtonGeneric } from "./ButtonGeneric.ts";

export class ButtonPick extends ButtonGeneric {
  number_picks: number = 1;
  static get action(): string {
    return "pick";
  }
  get action(): string {
    return (this.constructor as typeof ButtonPick)["action"];
  }
  connectedCallback() {
    this.innerHTML = `
            <button class="text-yellow-500 bg-gray-700 hover:bg-purple-800 hover:scale-110 px-8 p-2 rounded transform uppercase" ${ATTRIBUTE_TRANSLATION_KEY}="${
      (this.constructor as typeof ButtonPick)["action"]
    }!"></button>
        `;
    this.querySelector("button")!.addEventListener("click", (): void => {
      this.pick();
    });
  }
  async pick() {
    const picks = this["number_picks"];
    const isRisk: boolean = this.action === "risk";
    const pickResult: TokenColor[] = tokenBag.pick(picks, isRisk);
    if (!pickResult) {
      return;
    }
    const sectionToken = this.closest<NotTheEndSet>(NotTheEndSet.tag);
    if (!sectionToken) throw new Error(`Could not find ${NotTheEndSet.tag}`);
    const createResultText: (
      color: TokenColor,
      filteredResult: TokenColor[],
    ) => string = (color: TokenColor, filteredResult: TokenColor[]): string => {
      const imageUrl: string =
        (sectionToken.querySelector(`[token-color="${color}"]`) as InputToken)
          .imageUrl;
      const result: string = filteredResult
        .map((): string => `<img class="w-6 inline" src="${imageUrl}">`).join(
          "",
        );
      return result;
    };

    const getFilteredResult: (
      color: TokenColor,
      result: TokenColor[],
    ) => TokenColor[] = (
      color: TokenColor,
      result: TokenColor[],
    ): TokenColor[] => {
      return result.filter((token: TokenColor): boolean => token === color);
    };
    const drawResult: TokenColor[] = tokenBag.drawResult;
    const orangeResult: TokenColor[] = getFilteredResult("orange", pickResult);
    const purpleResult: TokenColor[] = getFilteredResult("purple", pickResult);

    const bagCompositionText: string = sectionToken.tokenInputs.map((
      input: InputToken,
    ): string =>
      `${input.value}<img class="w-6 inline" src="${input.imageUrl}">`
    ).join("");
    const tokenText: string = `token${picks > 1 ? "s" : ""}`;
    const text: string = `
            ${
      isRisk
        ? `took a risk for <b>${picks}</b> more ${tokenText}`
        : `drew <b>${picks}</b> ${tokenText}`
    }
            <br>${isRisk ? "original " : ""}bag (${bagCompositionText})
            <br>${
      isRisk
        ? `${
          createResultText("orange", getFilteredResult("orange", drawResult))
        }${createResultText("purple", getFilteredResult("purple", drawResult))}`
        : ""
    }${isRisk ? "+" : ""}${createResultText("orange", orangeResult)}${
      createResultText("purple", purpleResult)
    }
        `;
    const timestamp: number = Date.now();
    // const userDetailsController = await UserDetailsController.instance;
    // const nickname: string = await userDetailsController.nickname;
    const hidingResults = SwitchHideResult.active;
    // const connectionController = await ConnectionController.instance;
    // connectionController.sendMessage({
    //   from: nickname,
    //   timestamp,
    //   text: hidingResults
    //     ? `<span ${ATTRIBUTE_TRANSLATION_KEY}="did something in secret..."></span>`
    //     : text,
    //   type: "token",
    // });

    (document.querySelector(MessagesSection.tag) as MessagesSection).add({
      from: "You",
      timestamp,
      text: text +
        (hidingResults
          ? `<span ${ATTRIBUTE_TRANSLATION_KEY}="hidden to others"><span>`
          : ""),
      type: "token",
    });

    if (orangeResult.length === 1 && purpleResult.length === 9 && picks === 1) {
      if (globalThis.speechSynthesis) {
        globalThis.speechSynthesis.speak(
          new SpeechSynthesisUtterance(`luckylucky!`),
        );
      }
    }
    const pickAudios: HTMLAudioElement[] = [
      "pick1",
      "pick2",
      "pick3",
      "pick4",
      "pick5",
    ].map(mapAudio);
    const tokenAudios: HTMLAudioElement[] = ["token1", "token2", "token3"].map(
      mapAudio,
    );
    const pickAudio: HTMLAudioElement =
      pickAudios[Math.floor(Math.random() * pickAudios.length)];
    pickAudio.play();
    const tokenAudio: HTMLAudioElement =
      tokenAudios[Math.floor(Math.random() * tokenAudios.length)];
    const audioTimeout = setTimeout(() => {
      tokenAudio.play();
      clearTimeout(audioTimeout);
    }, 500);
    if (isRisk || !tokenBag.canRisk) {
      tokenBag.empty();
    }
  }
}
