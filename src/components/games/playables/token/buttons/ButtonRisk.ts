import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NotTheEndSet } from "../../../sets/NotTheEndSet.ts";
import { InputDraw } from "../inputs/InputDraw.ts";
import { tokenBag } from "../TokenBag.ts";
import { ButtonPick } from "./ButtonPick.ts";

export class ButtonRisk extends ButtonPick {
  static override get action(): string {
    return "risk";
  }
  get picks(): number {
    const sectionToken = this.closest<NotTheEndSet>(
      NotTheEndSet.tag,
    )!;
    const alreadyDrawn: number =
      (sectionToken.querySelector(InputDraw.tag) as InputDraw).value;
    const remainingTokensToRisk: number = 5 - alreadyDrawn;
    const remainingTokensInBag: number = tokenBag.content.length;
    const picks: number = remainingTokensToRisk > remainingTokensInBag
      ? remainingTokensInBag
      : remainingTokensToRisk;
    return picks;
  }
  override pick() {
    super.pick();
    tokenBag.empty();
    return Promise.resolve();
  }
}
safeCustomDefine(ButtonRisk);
