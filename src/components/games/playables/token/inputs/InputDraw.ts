import { store } from "store";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NotTheEndSet } from "../../../sets/NotTheEndSet.ts";
import { InputGeneric } from "./InputGeneric.ts";

export class InputDraw extends InputGeneric {
  override connectedCallback() {
    this.innerHTML = `
            <div class="grid grid-cols-3">
                <div class="place-self-center col-span-2" title="drawing"><span>DRAW</span></div>
                <input class="col-span-1 place-self-center text-center w-full" type="number" min="1" step="1" required>
            </div>
        `;
    super.connectedCallback();
    return Promise.resolve();
  }
  updateMax(): void {
    const input = this.querySelector<HTMLInputElement>("input")!;
    const sectionToken: NotTheEndSet = this.closest(
      NotTheEndSet.tag,
    ) as NotTheEndSet;
    const totalTokens: number = sectionToken.totalTokens;
    const maxDraw: number = totalTokens > 4 ? 4 : totalTokens;
    input.max = maxDraw + "";
    const inputValue: number = Number(input.value);
    if (inputValue > maxDraw) {
      input.value = maxDraw + "";
    }
    store.setItem(input.id, input.value);
  }
}
safeCustomDefine(InputDraw);
