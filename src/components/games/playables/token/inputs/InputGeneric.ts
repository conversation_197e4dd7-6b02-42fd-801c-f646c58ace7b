import { store } from "store";
import { ShowHideElement } from "utils/ShowHideElement.ts";
import { NotTheEndSet } from "../../../sets/NotTheEndSet.ts";
import { InputDraw } from "./InputDraw.ts";

export class InputGeneric extends ShowHideElement {
  set disabled(isDisabled: boolean) {
    const input = this.querySelector<HTMLInputElement>("input")!;
    input.disabled = isDisabled;
    input.title = isDisabled
      ? `Cannot change the bag's content, risk or empty the bag first.`
      : "";
  }
  get isValid(): boolean {
    const sectionToken: NotTheEndSet = this.closest(
      NotTheEndSet.tag,
    ) as NotTheEndSet;
    const isBagEmpty: boolean = sectionToken.totalTokens < 1;
    const input: HTMLInputElement = this.querySelector(
      "input",
    ) as HTMLInputElement;
    input.setCustomValidity(isBagEmpty ? "Cannot draw from empty bag." : "");
    return input.reportValidity();
  }
  set value(value: number) {
    const input: HTMLInputElement = this.querySelector(
      "input",
    ) as HTMLInputElement;
    input.value = value + "";
    input.dispatchEvent(new Event("change"));
  }
  get value(): number {
    return Number(this.querySelector("input")!.value);
  }
  async connectedCallback() {
    const input = this.querySelector<HTMLInputElement>("input")!;
    const localforageId: string = input.id;
    const storedValue: number | null = await store.getItem(localforageId);
    this.value = Number(storedValue) ?? 1;
    input.addEventListener("change", (): void => {
      this.checkValidity();
      const sectionToken: NotTheEndSet = this.closest(
        NotTheEndSet.tag,
      ) as NotTheEndSet;
      store.setItem(localforageId, this.value);
      (sectionToken.querySelector<InputDraw>(InputDraw.tag)!).updateMax();
      sectionToken.refreshTotalTokens();
    });
  }
  empty(): void {
    const input: HTMLInputElement = this.querySelector(
      "input",
    ) as HTMLInputElement;
    input.value = 0 + "";
  }
  checkValidity(): void {
    this.isValid;
  }
}
