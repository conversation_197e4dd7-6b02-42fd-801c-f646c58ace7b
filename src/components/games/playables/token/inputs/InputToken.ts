import { GL<PERSON><PERSON><PERSON> } from "GLOBALS";
import { TokenColor, TokenTypeColor } from "types/NotTheEnd.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { InputGeneric } from "./InputGeneric.ts";

export class InputToken extends InputGeneric {
  get color(): TokenTypeColor {
    return this.getAttribute("token-color") as TokenTypeColor;
  }
  get tokens(): TokenColor[] {
    const tokens: unknown[] = Array.from({ length: this.value });
    if (this.color === "gray") {
      const tokenTypes: TokenColor[] = ["orange", "purple"];
      return tokens.map((): TokenColor =>
        tokenTypes[Math.floor(Math.random() * 2)]
      );
    } else {
      return (tokens.fill(this.color) as TokenColor[]);
    }
  }
  imageUrl = `public/assets/img/token/RPGMeet_NtE-${this.color}-token.svg`;
  override connectedCallback() {
    this.innerHTML = `
            <div class="flex">
                <input id="${GLOBALS.PREFIX}-token-input_${this.color}" type="number" min="0" step="1" class="m-auto w-1/3 text-center" title="${this.color}" required>
                <div class="place-self-center m-auto" title="${this.color}">
                    <img class="w-10 inline" src="${this.imageUrl}">
                </div>
            </div>`;
    super.connectedCallback();
    return Promise.resolve();
  }
}
safeCustomDefine(InputToken);
