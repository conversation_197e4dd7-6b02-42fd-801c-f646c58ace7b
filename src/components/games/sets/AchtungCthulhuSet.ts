import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { AchtungCthulhuChallengeDie } from "../playables/dice/die/AchtungCthulhuChallengeDie.ts";
import { AchtungCthulhuTestDie } from "../playables/dice/die/AchtungCthulhuTestDie.ts";
import { DiceSet } from "./DiceSet.ts";

export class AchtungCthulhuSet extends DiceSet {
  static override get label(): string {
    return `Achtung! Cthulhu™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get columnNumber(): number {
    return 2;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const testDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `test`,
      elements: [{
        type: "die",
        tag: AchtungCthulhuTestDie.tag,
        name: "test",
        size: 20,
        min: 2,
        max: 5,
      }],
    };
    const challengeDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `challenge`,
      elements: [{
        type: "die",
        tag: AchtungCthulhuChallengeDie.tag,
        name: "challenge",
        size: 6,
      }],
    };

    return [
      testDiceGroup,
      challengeDiceGroup,
    ];
  }
}
safeCustomDefine(AchtungCthulhuSet);
