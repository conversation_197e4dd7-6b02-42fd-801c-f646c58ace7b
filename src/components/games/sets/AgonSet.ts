import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { AgonDie } from "../playables/dice/die/AgonDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

export class AgonSet extends DiceSet {
  static override get label(): string {
    return `Agon™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get columnNumber(): number {
    return 4;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const adversityGroupRollId = `${DiceGroupRollButton.tag}-agon-adversity`;
    const adversityDice: DieSettings[] = [6, 8, 10, 12].map((size: number) => ({
      type: "die",
      tag: AgonDie.tag,
      name: "adversity",
      size,
      min: 0,
      groupRoll: {
        id: adversityGroupRollId,
      },
      singleThrowDisabled: true,
    }));
    const adversityGroupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: adversityGroupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "size" },
      resultSort: "descending",
      span: adversityDice.length,
      additionalClasses: ["mb-2"],
    };
    const adversityDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `adversity`,
      columNumber: 4,
      span: 6,
      elements: [...adversityDice, adversityGroupRollButton],
      collapsible: true,
    };
    const heroGroupRollId = `${DiceGroupRollButton.tag}-agon-hero`;
    const heroDice: DieSettings[] = [4, 6, 8, 10, 12].map((size: number) => ({
      type: "die",
      tag: AgonDie.tag,
      name: "hero",
      size,
      min: 0,
      groupRoll: {
        id: heroGroupRollId,
      },
      singleThrowDisabled: true,
    }));
    const heroGroupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: heroGroupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "size" },
      resultSort: "descending",
      span: heroDice.length,
      additionalClasses: ["mb-2"],
    };
    const heroDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `hero`,
      columNumber: 4,
      span: 6,
      elements: [...heroDice, heroGroupRollButton],
      collapsible: true,
    };
    return [
      adversityDiceGroup,
      heroDiceGroup,
    ];
  }
}
safeCustomDefine(AgonSet);
