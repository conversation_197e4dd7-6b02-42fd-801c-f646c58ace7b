import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { AvatarDie } from "../playables/dice/die/AvatarDie.ts";
import { PBTAFlavoredSet } from "sets/PBTAFlavoredSet.ts";

export class AvatarSet extends PBTAFlavoredSet {
  static override get label(): string {
    return `Avatar Legends™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  static override baseDie = AvatarDie;
}
safeCustomDefine(AvatarSet);
