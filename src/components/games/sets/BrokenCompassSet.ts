import { DiceGroupSettings, DieSettings } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { BrokenCompassDie } from "../playables/dice/die/BrokenCompassDie.ts";
import { BrokenCompassLuckCoin } from "../playables/dice/die/BrokenCompassLuckCoin.ts";
import { DiceSet } from "./DiceSet.ts";

export class BrokenCompassSet extends DiceSet {
  static override get label(): string {
    return `Broken Compass™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get elements(): (DieSettings | DiceGroupSettings | string)[] {
    return [
      {
        type: "die",
        tag: BrokenCompassDie.tag,
        name: "die",
        size: 6,
      },
      {
        type: "die",
        tag: BrokenCompassLuckCoin.tag,
        name: "luck coin",
        size: 2,
        single: true,
      },
    ];
  }
}
safeCustomDefine(BrokenCompassSet);
