import { assertEquals, assertExists } from "@std/assert";
import { CBRPNKSet } from "./CBRPNKSet.ts";
import { CBRPNKDie } from "../playables/dice/die/CBRPNKDie.ts";
import { DieSettings, DiceGroupRollButtonSettings } from "types/models.ts";

Deno.test("CBRPNKSet", async (t) => {
  await t.step("should have correct label", () => {
    assertEquals(CBRPNKSet.label, "CBR+PNK™️");
  });

  await t.step("should not be translatable", () => {
    assertEquals(CBRPNKSet.translatable, false);
  });

  await t.step("should have correct column number", () => {
    const set = new CBRPNKSet();
    assertEquals(set.columnNumber, 9);
  });

  await t.step("should have correct elements configuration", () => {
    const set = new CBRPNKSet();
    const elements = set.elements;
    
    // Should have 6 dice + 1 group roll button = 7 elements
    assertEquals(elements.length, 7);
    
    // First 6 elements should be dice
    for (let i = 0; i < 6; i++) {
      const element = elements[i] as DieSettings;
      assertEquals(element.type, "die");
      assertEquals(element.tag, CBRPNKDie.tag);
      assertEquals(element.name, "d6");
      assertEquals(element.size, 6);
      assertEquals(element.single, true);
      assertEquals(element.singleThrowDisabled, true);
      assertExists(element.groupRoll);
      assertEquals(element.groupRoll.optional, true);
    }
    
    // Last element should be group roll button
    const groupButton = elements[6] as DiceGroupRollButtonSettings;
    assertEquals(groupButton.type, "group-button");
    assertEquals(groupButton.buttonText, "roll");
    assertEquals(groupButton.resultConsolidation.strategy, "individual");
    assertEquals(groupButton.resultSort, "descending");
    assertEquals(groupButton.span, 3);
  });

  await t.step("should have a valid tag", () => {
    assertExists(CBRPNKSet.tag);
    assertEquals(typeof CBRPNKSet.tag, "string");
  });
});

Deno.test("CBRPNKDie", async (t) => {
  await t.step("should have correct sides", () => {
    const die = new CBRPNKDie();
    const sides = die.sides;
    assertEquals(sides.length, 6);
    assertEquals(sides[0], "RPGMeet_CBR-PNK-1.svg");
    assertEquals(sides[5], "RPGMeet_CBR-PNK-6.svg");
  });

  await t.step("should sort results descending", () => {
    const die = new CBRPNKDie();
    assertEquals(die.sortResults, "descending");
  });

  await t.step("should have cyberpunk audio", () => {
    const die = new CBRPNKDie();
    const resultAudios = die.resultAudios;
    assertEquals(resultAudios.includes("die-cyber-1"), true);
  });
});
