import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { CityOfMistDie } from "../playables/dice/die/CityOfMistDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-city-of-mist`;

export class CityOfMistSet extends DiceSet {
  static override get label(): string {
    return `City Of Mist™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get columnNumber(): number {
    return 9;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const dice: DieSettings[] = ["logos", "mythos"].map((name: string) => ({
      type: "die",
      tag: CityOfMistDie.tag,
      name,
      size: 6,
      single: true,
      span: 2,
      groupRoll: {
        id: groupRollId,
      },
      singleThrowDisabled: true,
    }));
    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "name", sum: true },
      span: 4,
    };
    return [
      `<span class="col-span-1"></span>`,
      ...dice,
      groupRollButton,
    ];
  }
}
safeCustomDefine(CityOfMistSet);
