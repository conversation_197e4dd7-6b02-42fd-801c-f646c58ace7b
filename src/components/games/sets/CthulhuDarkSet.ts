import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { CthulhuDarkDie } from "../playables/dice/die/CthulhuDarkDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-cthulhu-dark`;

export class CthulhuDarkSet extends DiceSet {
  static override get label(): string {
    return `Cthulhu Dark™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get columnNumber(): number {
    return 3;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const dice = ["human", "profession", "insight"]
      .map((name: string): DieSettings => {
        return {
          type: "die",
          tag: CthulhuDarkDie.tag,
          name,
          size: 6,
          single: true,
          singleThrowDisabled: true,
          groupRoll: {
            id: groupRollId,
            optional: true,
          },
          buttonText: {
            text: `6`,
          },
        };
      });
    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      span: dice.length,
      resultConsolidation: { strategy: "individual" },
      resultSort: "descending",
    };
    return [
      ...dice,
      groupRollButton,
    ];
  }
}
safeCustomDefine(CthulhuDarkSet);
