import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Deck } from "../playables/deck/Deck.ts";
import { DeckStatus } from "../playables/deck/DeckStatus.ts";
import { ResetDeck } from "../playables/deck/ResetDeck.ts";
import { GameSet } from "./GameSet.ts";

export class DeckOfCards extends GameSet {
  static override get label(): string {
    return `Deck of Cards`;
  }
  connectedCallback() {
    this.className = `grid grid-cols-7`;
    this.innerHTML = `
            <${Deck.tag} die-size="54" die-name="Deck" class="col-span-3"></${Deck.tag}>
            <div class="col-span-4 grid grid-rows-2 justify-items-center items-center">
                <${DeckStatus.tag}></${DeckStatus.tag}>
                <${ResetDeck.tag}></${ResetDeck.tag}>
            </div>
        `;
    return Promise.resolve();
  }
}
safeCustomDefine(DeckOfCards);
