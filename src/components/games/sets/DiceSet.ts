import { store } from "store";
import { DEFAULT_FONT_STYLE, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "GL<PERSON><PERSON><PERSON>";
import {
  ATTRIBUTE_TRANSLATION_KEY,
  TranslationKey,
  TRANSLATIONS,
} from "TRANSLATIONS";
import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
  FontStyleKey,
} from "types/models.ts";
import { SwitchHideResult } from "utils/SwitchHideResult.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { GameSet } from "./GameSet.ts";

export class DiceSet extends GameSet {
  get collapsible(): boolean {
    return false;
  }
  get columnNumber(): number {
    return this.elements.length < 5 ? this.elements.length : 5;
  }
  get elements(): (
    | DieSettings
    | DiceGroupSettings
    | SwitchHideResult
    | DiceGroupRollButtonSettings
    | string
  )[] {
    return [];
  }
  connectedCallback() {
    const renderDieSettings: (dieSettings: DieSettings) => string = (
      dieSettings: DieSettings,
    ) => {
      const tileStyle = DEFAULT_FONT_STYLE;
      for (const key in dieSettings?.buttonText?.style) {
        tileStyle[key as FontStyleKey] =
          dieSettings.buttonText.style[key as FontStyleKey];
      }
      const fontString = Object.keys(tileStyle).map((key) =>
        `${key}: ${tileStyle[key as FontStyleKey]}`
      ).join(";");
      const dieHTML = `<${dieSettings.tag} 
                die-size="${dieSettings.size}" 
                die-name="${dieSettings.name}" 
                ${dieSettings.single ? "single-die" : ""}
                ${
        dieSettings.singleThrowDisabled ? GLOBALS.singleThrowDisabledId : ""
      }
                min="${dieSettings.min ?? 1}"
                max="${dieSettings.max ?? 20}"
                step="${dieSettings.step ?? 1}"
                label-text="${dieSettings.label?.text ?? ""}"
                label-position="${dieSettings.label?.position ?? "top"}"
                ${
        dieSettings.groupRoll?.id
          ? `${DiceGroupRollButton.tag}="${dieSettings.groupRoll?.id}"`
          : ""
      }
                ${
        dieSettings.groupRoll?.optional
          ? `${DiceGroupRollButton.tag}-optional`
          : ""
      }
                ${dieSettings.span ? `col-span="${dieSettings.span}"` : ""}
                ${dieSettings.hasSum ? `has-sum="true"` : ""}
                ${dieSettings.color ? `color="${dieSettings.color}"` : ""}
                ${
        dieSettings.buttonText?.text
          ? `button-text="${dieSettings.buttonText.text}"`
          : ""
      }
                button-text-font-string="${fontString}"
                ${dieSettings.mod ? `mod="${dieSettings.mod}"` : ""}
                ${dieSettings.flavor ? `flavor="${dieSettings.flavor}"` : ""}
            ></${dieSettings.tag}>`;
      return dieHTML;
    };
    const renderGroupButton: (
      groupButtonSettings: DiceGroupRollButtonSettings,
    ) => string = (groupButtonSettings: DiceGroupRollButtonSettings) => {
      const groupRollButtonSettings: DiceGroupRollButtonSettings =
        groupButtonSettings as DiceGroupRollButtonSettings;
      const buttonText = groupRollButtonSettings.buttonText || "roll";
      const span = groupRollButtonSettings.span
        ? `col-span-${groupRollButtonSettings.span}`
        : "";
      const classesHTML = [
        ...(groupRollButtonSettings.additionalClasses || []),
        span,
      ]
        .join(" ");
      const consolidation = groupRollButtonSettings.resultConsolidation;
      const consolidationString =
        `result-consolidation="${consolidation.strategy}"`;
      const consolidationSumString = "sum" in consolidation
        ? `result-consolidation-sum="true"`
        : "";
      const sorting = groupRollButtonSettings.resultSort;
      const sortingString = sorting ? `result-sorting="${sorting}"` : "";
      const flavor = groupRollButtonSettings.flavor;
      const flavorString = flavor ? `flavor="${flavor}"` : "";
      const html = `
                <${DiceGroupRollButton.tag}
                    id="${groupRollButtonSettings.id}"
                    button-text="${buttonText}"
                    class="${classesHTML}"
                    ${consolidationString}
                    ${consolidationSumString}
                    ${sortingString}
                    ${flavorString}
                >
                </${DiceGroupRollButton.tag}>
            `;
      return html;
    };
    const diceHTML: string = this.elements
      .map((settings) => {
        if (typeof settings === "string") {
          return settings;
        }
        if ("type" in settings && settings.type === "group") {
          const diceGroupSettings = settings as DiceGroupSettings;
          const elementsHTML = diceGroupSettings.elements
            .map(
              (
                element: DieSettings | DiceGroupRollButtonSettings,
              ): string => {
                return element.type === "die"
                  ? renderDieSettings(element as DieSettings)
                  : renderGroupButton(element as DiceGroupRollButtonSettings);
              },
            )
            .join("");
          const legend = diceGroupSettings.legend;
          const translatableLegend = legend in TRANSLATIONS
            ? TRANSLATIONS[legend as TranslationKey]
            : legend;
          return `
                        <fieldset 
                            class="flex border col-span-${
            diceGroupSettings.span || diceGroupSettings.elements.length
          } text-sm text-center" 
                            ${
            diceGroupSettings.collapsible ? "collapsible" : ""
          }
                        >
                            <legend 
                                ${
            diceGroupSettings.collapsible
              ? `collapser_legend class="cursor-pointer"`
              : ""
          } ${
            translatableLegend
              ? `${ATTRIBUTE_TRANSLATION_KEY}="${diceGroupSettings.legend}"`
              : ""
          }
                            >
                                ${
            translatableLegend ? "" : diceGroupSettings.legend
          }
                            </legend>
                            <div
                                fieldset_content                                         ${
            diceGroupSettings.collapsed ? "hidden" : ""
          }
                                class="grid grid-cols-${
            diceGroupSettings.columNumber || diceGroupSettings.elements.length
          } gap-2 w-full ${
            diceGroupSettings.additionalClasses?.join(" ") || " "
          }"
                            >
                                ${elementsHTML}
                            </div>
                        </fieldset>
                            `;
        }
        if ("type" in settings && settings.type === "die") {
          return renderDieSettings(settings as DieSettings);
        }
        if ("type" in settings && settings.type === "group-button") {
          return renderGroupButton(settings as DiceGroupRollButtonSettings);
        }
      }).join("");
    this.innerHTML = `
                        <div 
                            container_dice 
                            class="grid grid-cols-${this.columnNumber} gap-2"
                        >
                            ${diceHTML}
                        </div>
                        `;
    this.querySelectorAll<HTMLFieldSetElement>("fieldset[collapsible]").forEach(
      async (fieldset) => {
        const legend = fieldset.querySelector("legend") as HTMLLegendElement;
        const content = fieldset.querySelector(
          `[fieldset_content]`,
        ) as HTMLDivElement;
        const legendText = legend.getAttribute(ATTRIBUTE_TRANSLATION_KEY) ||
          legend.innerText;
        const idLocalForage =
          `${GLOBALS.PREFIX}-dice-set-collapsed-${legendText}`;
        const collapsed = await store.getItem(idLocalForage);
        if (collapsed) {
          content.classList.add("hidden");
        }
        legend.addEventListener("click", async () => {
          content.classList.toggle("hidden");
          await store.setItem(
            idLocalForage,
            content.classList.contains("hidden"),
          );
        });
      },
    );
  }
}
