import { DEFAULT_FONT_STYLE } from "GLOBALS";
import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { PolyDie } from "../playables/dice/die/PolyhedricDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-dont-rest-your-head`;

export class DontRestYourHeadSet extends DiceSet {
  static override get label(): string {
    return `Don't Rest Your Head`;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const dieOptions = {
      white: {
        min: 1,
        max: 3,
      },
      black: {
        min: 0,
        max: 6,
      },
      red: {
        min: 0,
        max: 6,
      },
      gray: {
        min: 0,
        max: 3,
      },
      purple: {
        min: 1,
        max: 20,
      },
    };
    const dice: DieSettings[] = Object.keys(dieOptions)
      .map((color) => {
        const options = dieOptions[color as keyof typeof dieOptions];
        return {
          type: "die",
          tag: PolyDie.tag,
          name: color,
          color,
          size: 6,
          hasSum: false,
          min: options.min,
          max: options.max,
          groupRoll: {
            id: groupRollId,
          },
          singleThrowDisabled: true,
          buttonText: {
            text: `6`,
            style: {
              ...DEFAULT_FONT_STYLE,
              fill: color === "white" ? "black" : "white",
              stroke: color === "white" ? "black" : "white",
            },
          },
        };
      });
    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "individual" },
      span: 5,
    };
    return [...dice, groupRollButton];
  }
}
safeCustomDefine(DontRestYourHeadSet);
