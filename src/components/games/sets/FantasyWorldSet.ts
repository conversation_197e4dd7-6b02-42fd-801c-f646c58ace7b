import { DiceGroupRollButtonSettings, DieSettings } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { FantasyWorldDie } from "../playables/dice/die/FantasyWorldDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-fantasy-world`;

export class FantasyWorldSet extends DiceSet {
  static override get label(): string {
    return `Fantasy World™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get elements(): (DieSettings | DiceGroupRollButtonSettings)[] {
    const dieSettings: DieSettings = {
      type: "die",
      tag: FantasyWorldDie.tag,
      size: 6,
      name: "d6",
      singleThrowDisabled: true,
      single: true,
      groupRoll: {
        id: groupRollId,
        optional: false,
      },
    };
    const optionalDie: DieSettings = {
      ...dieSettings,
      groupRoll: {
        id: groupRollId,
        optional: true,
      },
    };
    const dice: DieSettings[] = [dieSettings, dieSettings, optionalDie];
    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "name" },
      resultSort: "descending",
    };
    return [
      ...dice,
      groupRollButton,
    ];
  }
}
safeCustomDefine(FantasyWorldSet);
