import { DiceGroupSettings, DieSettings } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { FudgeDie } from "../playables/dice/die/FudgeDie.ts";
import { DiceSet } from "./DiceSet.ts";

export class FateSet extends DiceSet {
  static override get label(): string {
    return `Fate™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get elements(): (DieSettings | DiceGroupSettings | string)[] {
    return [
      {
        type: "die",
        tag: FudgeDie.tag,
        name: "fudge",
        size: 6,
      },
    ];
  }
}
safeCustomDefine(FateSet);
