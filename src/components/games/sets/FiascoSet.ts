import { DEFAULT_FONT_STYLE } from "GLOBALS";
import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { PolyDie } from "../playables/dice/die/PolyhedricDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-fiasco`;

export class FiascoSet extends DiceSet {
  static override get label(): string {
    return `Fiasco™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const dice: DieSettings[] = ["black", "white"]
      .map((color) => {
        return {
          type: "die",
          tag: PolyDie.tag,
          name: color,
          color,
          size: 6,
          hasSum: false,
          min: 0,
          groupRoll: {
            id: groupRollId,
          },
          singleThrowDisabled: true,
          buttonText: {
            text: `6`,
            style: {
              ...DEFAULT_FONT_STYLE,
              fill: color === "white" ? "#454545" : "white",
              stroke: color === "white" ? "#454545" : "white",
            },
          },
        };
      });
    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "individual" },
    };
    return [...dice, groupRollButton];
  }
}
safeCustomDefine(FiascoSet);
