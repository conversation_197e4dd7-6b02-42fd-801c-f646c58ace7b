import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { PolyDie } from "../playables/dice/die/PolyhedricDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-gurps`;

export class GURPSSet extends DiceSet {
  static override get label(): string {
    return `GURPS™`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get columnNumber(): number {
    return 4;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const dice: DieSettings[] = [6, 6, 6].map((size: number) => ({
      type: "die",
      tag: PolyDie.tag,
      name: "d6",
      size,
      single: true,
      singleThrowDisabled: true,
      groupRoll: {
        id: groupRollId,
      },
    }));
    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
            resultConsolidation: { strategy: "size", sum: true },
                  resultSort: "descending",
    };
    const diceGroup: DiceGroupSettings = {
      type: "group",
      legend: `task/resolution`,
      elements: [...dice, groupRollButton],
      span: 3
    };
    const choosableDice: DieSettings = {
      type: "die",
      tag: PolyDie.tag,
      name: "d6",
      size: 6,
      min: 1,
      max: 20,
      hasSum: true
    };
    const choosableDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `damage`,
      elements: [choosableDice],
    };
    return [
      diceGroup,
      choosableDiceGroup,
    ];
  }
}
safeCustomDefine(GURPSSet);
