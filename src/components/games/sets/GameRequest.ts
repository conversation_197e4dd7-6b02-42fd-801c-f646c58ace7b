import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "G<PERSON><PERSON><PERSON><PERSON>";
import { ATTRIBUTE_TRANSLATION_KEY, TRANSLATIONS } from "TRANSLATIONS";
import { devLog } from "utils/devLog.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { GameSet } from "./GameSet.ts";
import { STATUS_CODE } from "@std/http/status";
import { TranslationController } from "controllers/TranslationController.ts";
import { Notify, Report } from "notiflix";
import { Login } from "components/core/Login.ts";

export class GameRequest extends GameSet {
  static override get label(): string {
    return "Suggest Game";
  }
  static override get optionAdditionalStyles(): string[] {
    return ["font-bold"];
  }
  async connectedCallback() {
    this.innerHTML = `
                <form class="grid gap-y-2">
                    <div class="grid">
                        <label 
                            for="gameRequestName" 
                            ${ATTRIBUTE_TRANSLATION_KEY}="gameRequestName"
                        ></label>
                       <input 
                            name="gameRequestName" 
                            type="text" 
                            required 
                            class="border"
                        >
                    </div>
                    <div class="grid">
                        <label 
                            for="gameRequestAdditionalDetails" 
                            ${ATTRIBUTE_TRANSLATION_KEY}="gameRequestAdditionalDetails"></label>
                        <textarea 
                            name="gameRequestAdditionalDetails" 
                            type="text" 
                            class="border"
                        ></textarea>
                    </div>
                    <div class="grid">
                        <label 
                            for="gameRequestAdditionalLink" 
                            ${ATTRIBUTE_TRANSLATION_KEY}="gameRequestAdditionalLink"
                        ></label>
                        <input 
                            name="gameRequestAdditionalLink" 
                            type="url"
                            class="border"
                        >
                    </div>
                    <div class="grid">
                        <button 
                            type="submit" 
                            ${ATTRIBUTE_TRANSLATION_KEY}="gameRequestSubmit" 
                            class="${GLOBALS.buttonClasses}"
                            disabled="disabled"
                        ></button>
                    </div>
                </form>
            `;
    const inputs = [
      ...Array.from(this.querySelectorAll("input")),
      ...Array.from(this.querySelectorAll("textarea")),
    ];
    // on change check if the form is valid, then enable/disable the submit button
    const form = this.querySelector("form") as HTMLFormElement;
    const submitButton = this.querySelector("button") as HTMLButtonElement;
    const buttonClasses = {
      enabled: ["bg-blue-900", "hover:bg-blue-600", "hover:scale-110"],
      disabled: ["bg-gray-900"],
    };
    const submit = {
      disable: () => {
        submitButton.setAttribute("disabled", "disabled");
        submitButton.classList.remove(...buttonClasses.enabled);
        submitButton.classList.add(...buttonClasses.disabled);
      },
      enable: () => {
        submitButton.removeAttribute("disabled");
        submitButton.classList.remove(...buttonClasses.disabled);
        submitButton.classList.add(...buttonClasses.enabled);
      },
    };
    const validateForm = () => {
      const isValid = form.checkValidity();
      submit[isValid ? "enable" : "disable"]();
    };
    validateForm();
    inputs.forEach((input) => {
      input.addEventListener("input", validateForm);
    });
    form.addEventListener("submit", async (e) => {
      e.preventDefault();
      submit.disable();
      const translationController = await TranslationController.instance;
      const language = await translationController.language;
      const notifyFailure = async () => {
        Report.failure(
          TRANSLATIONS.error.values.text[language],
          TRANSLATIONS.gameRequestError.values.text[language],
          TRANSLATIONS.confirm.values.text[language],
        );
      };
      try {
        const formData = new FormData(form);
        const requestDetailsIds = [
          "gameRequestAdditionalDetails",
          "gameRequestAdditionalLink",
        ];
        const body = requestDetailsIds.map((id) => formData.get(id)).filter(
          Boolean,
        ).join("\n") + "\n" + await Login.email;
        const requestBody = {
          name: formData.get("gameRequestName") as string,
          body: body,
        };
        const url = `${globalThis.location.origin}/api/request-game`;
        const request = await fetch(url, {
          method: "POST",
          mode: "cors",
          body: JSON.stringify(requestBody),
          headers: {
            "Content-Type": "application/json",
          },
          redirect: "follow",
        });
        const response = await request.text();
        devLog({ response });
        if (request.status === STATUS_CODE.Created) {
          Notify.success(
            TRANSLATIONS["gameRequestSuccess"].values.text[language],
            () => null,
            {
              timeout: 2000,
            },
          );
          form.reset();
        } else {
          notifyFailure();
        }
      } catch (_e) {
        notifyFailure();
      } finally {
        submit.enable();
      }
    });
  }
}
safeCustomDefine(GameRequest);
