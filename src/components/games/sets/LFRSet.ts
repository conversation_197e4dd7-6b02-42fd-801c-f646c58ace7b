import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { LFRDie } from "../playables/dice/die/LFRDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-lfr`;
export class LFRSet extends DiceSet { 
  static override get label(): string {
    return `Legend of the Five Rings`;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const dice: DieSettings[] = [{
      type: "die",
      tag: LFRDie.tag,
      name: "ring die",
      size: 6,
      singleThrowDisabled: true,
      groupRoll: {
        id: groupRollId,
        optional: false,
      },
    }, {
      type: "die",
      tag: LFRDie.tag,
      name: "skill die",
      size: 12,
      min: 0,
      singleThrowDisabled: true,
      groupRoll: {
        id: groupRollId,
        optional: false,
      },
    }];
    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "individual" },
    };
    return [
      ...dice,
      groupRollButton,
    ];
  }
}
safeCustomDefine(LFRSet);
