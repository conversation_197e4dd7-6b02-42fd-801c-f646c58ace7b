import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { BagIcon } from "../playables/token/BagIcon.ts";
import { ButtonDraw } from "../playables/token/buttons/ButtonDraw.ts";
import { ButtonEmpty } from "../playables/token/buttons/ButtonEmpty.ts";
import { ButtonRisk } from "../playables/token/buttons/ButtonRisk.ts";
import { InputDraw } from "../playables/token/inputs/InputDraw.ts";
import { InputToken } from "../playables/token/inputs/InputToken.ts";
import { tokenBag } from "../playables/token/TokenBag.ts";
import { GameSet } from "./GameSet.ts";

export class NotTheEndSet extends GameSet {
  static override get label(): string {
    return "Not the End™️";
  }
  static override get translatable(): boolean {
    return false;
  }
  get tokenInputs(): InputToken[] {
    return Array.from(this.querySelectorAll<InputToken>(InputToken.tag));
  }
  get totalTokens(): number {
    const totalTokens: number = this.tokenInputs
      .map((input: InputToken): number => input.value)
      .reduce((sum: number, x: number): number => sum + x);
    (this.querySelector(BagIcon.tag) as BagIcon).full = totalTokens > 0;
    return totalTokens;
  }
  connectedCallback() {
    this.innerHTML = `
            <fieldset class="border rounded">
                <legend ${ATTRIBUTE_TRANSLATION_KEY}="bag" class="uppercase"></legend>
                <div class="grid grid-cols-3 gap-2 p-3">
                    <${InputToken.tag} token-color="orange"></${InputToken.tag}>
                    <${InputToken.tag} token-color="purple"></${InputToken.tag}>
                    <${InputToken.tag} token-color="gray"></${InputToken.tag}>
                </div>
            </fieldset>
            <div class="grid grid-cols-5 gap-2 p-3">
                <${BagIcon.tag} class="col-span-1"></${BagIcon.tag}>
                <${InputDraw.tag} class="col-span-2 m-auto"></${InputDraw.tag}>
                <${ButtonDraw.tag} class="col-span-2 m-auto"></${ButtonDraw.tag}>
                <${ButtonEmpty.tag} class="col-span-2 m-auto"></${ButtonEmpty.tag}>
                <${ButtonRisk.tag} class="col-span-2 m-auto"></${ButtonRisk.tag}>
            </div>
        `;
    this.switchSetup();
    Promise.resolve();
  }
  switchSetup(): boolean {
    const conditionRisk: boolean = tokenBag.canRisk &&
      this.querySelector<ButtonRisk>(ButtonRisk.tag)!.picks > 0;
    try {
      this.querySelector<ButtonDraw>(ButtonDraw.tag)!
        [conditionRisk ? "hide" : "show"]();
      this.querySelector<InputDraw>(InputDraw.tag)!
        [conditionRisk ? "hide" : "show"]();
      this.querySelector<ButtonRisk>(ButtonRisk.tag)!
        [conditionRisk ? "show" : "hide"]();
      this.querySelector<ButtonEmpty>(ButtonEmpty.tag)!
        [conditionRisk ? "show" : "hide"]();
      this.tokenInputs.forEach((input: InputToken): void => {
        input.disabled = conditionRisk;
      });
      this.refreshTotalTokens();
    } catch {
      return false;
    }
    return conditionRisk;
  }
  refreshTotalTokens(): void {
    this.totalTokens;
  }
}
safeCustomDefine(NotTheEndSet);
