import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { DiceGroupSettings, DieSettings } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OrdemParanormalDie } from "../playables/dice/die/OrdemParanormalDie.ts";
import { DiceSet } from "./DiceSet.ts";

export class OrdemParanormalSet extends DiceSet {
  static override get label(): string {
    return `Ordem Paranormal`;
  }
  static override get translatable(): boolean {
    return true;
  }
  override get columnNumber(): number {
    return 3;
  }
  override get elements(): (DieSettings | DiceGroupSettings | string)[] {
    const testDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `tests`,
      elements: [
        {
          type: "die",
          tag: OrdemParanormalDie.tag,
          name: "d20",
          size: 20,
          min: 1,
          max: 10,
          hasSum: false,
        },
        {
          type: "die",
          tag: OrdemParanormalDie.tag,
          name: "d20 (2x)",
          size: 20,
          min: 2,
          max: 2,
          hasSum: false,
        },
        {
          type: "die",
          tag: OrdemParanormalDie.tag,
          name: "d20 (3x)",
          size: 20,
          min: 3,
          max: 3,
          hasSum: false,
        },
      ],
      columNumber: 3,
    };

    const damageDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `damage`,
      elements: [
        {
          type: "die",
          tag: OrdemParanormalDie.tag,
          name: "d4",
          size: 4,
          hasSum: true,
        },
        {
          type: "die",
          tag: OrdemParanormalDie.tag,
          name: "d6",
          size: 6,
          hasSum: true,
        },
        {
          type: "die",
          tag: OrdemParanormalDie.tag,
          name: "d8",
          size: 8,
          hasSum: true,
        },
        {
          type: "die",
          tag: OrdemParanormalDie.tag,
          name: "d10",
          size: 10,
          hasSum: true,
        },
        {
          type: "die",
          tag: OrdemParanormalDie.tag,
          name: "d12",
          size: 12,
          hasSum: true,
        },
      ],
      columNumber: 5,
    };

    const attributeTestsTitle =
      `<h3 class="col-span-3 text-center font-semibold text-lg mb-2" ${ATTRIBUTE_TRANSLATION_KEY}="attribute tests"></h3>`;

    return [
      attributeTestsTitle,
      testDiceGroup,
      damageDiceGroup,
    ];
  }
}
safeCustomDefine(OrdemParanormalSet);
