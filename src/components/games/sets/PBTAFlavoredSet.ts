import { PBTAGenericSet } from "sets/PBTAGenericSet.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { FlavoredDie } from "dice/die/FlavoredDie.ts";
import { store } from "store";
import { ATTRIBUTE_TRANSLATION_KEY, TRANSLATIONS } from "TRANSLATIONS";

export class PBTAFlavoredSet extends PBTAGenericSet {
  static baseDie: typeof FlavoredDie = FlavoredDie;
  static get flavors() {
    return this.baseDie.flavors;
  }

  override get elements() {
    const flavors = Object.keys(
      (this.constructor as typeof PBTAFlavoredSet).flavors,
    );
    const baseElements = JSON.parse(
      JSON.stringify(super["elements"]),
    ) as PBTAGenericSet["elements"];

    const elementsForEachFlavor = flavors.map((flavor) => {
      const flavorElements = baseElements.map((config) => {
        if (typeof config === "string") return config;
        const newConfig = JSON.parse(JSON.stringify(config));
        const isDieSettings = "tag" in newConfig;
        if (isDieSettings) {
          newConfig.tag =
            (this.constructor as typeof PBTAFlavoredSet).baseDie.tag;
          const alreadyFlavored = !!newConfig.flavor;
          if (alreadyFlavored) return newConfig;
          newConfig.flavor = flavor;
          if (newConfig.groupRoll) newConfig.groupRoll.id += `-${flavor}`;
        }
        const isButtonRoll = newConfig.type === "group-button";
        if (isButtonRoll) {
          newConfig.id += `-${flavor}`;
          newConfig.flavor = flavor;
        }
        return newConfig;
      });
      return flavorElements;
    });
    const newElements = elementsForEachFlavor.flat();

    const optionsHTML = flavors
      .map(
        (flavor) => {
          const isFlavorTranslated = flavor in TRANSLATIONS;
          return isFlavorTranslated
            ? `<option value="${flavor}" ${ATTRIBUTE_TRANSLATION_KEY}="${flavor}"></option>`
            : `<option value="${flavor}">${flavor}</option>`;
        },
      )
      .join("");
    const select = `<select class="text-black">${optionsHTML}</select>`;
    const columnsClass = this.columnNumber
      ? `col-span-${this.columnNumber}`
      : "";
    const selectContainer = `
      <div class="${columnsClass} grid justify-center grid-flow-col gap-2">
          <label>
            <span ${ATTRIBUTE_TRANSLATION_KEY}="flavor"></span>
          </label>
          ${select}
      </div>
    `;
    return [selectContainer, ...newElements];
  }
  override async connectedCallback() {
    await super.connectedCallback();
    const constructor = this.constructor as typeof PBTAFlavoredSet;
    const flavors = Object.keys(constructor.flavors);
    const storedFlavor = await store.getItem<string>(
      `${constructor.tag}-flavor`,
    );
    const select = this.querySelector("select")!;
    select.value = storedFlavor || flavors[0];
    const showFlavor = (flavor: string) => {
      this.querySelectorAll<HTMLElement>(`[flavor]`).forEach((element) => {
        const elementFlavor = element.getAttribute("flavor");
        const isSelectedFlavor = elementFlavor === flavor;
        element.classList.toggle("hidden", !isSelectedFlavor);
      });
    };
    const flavor = storedFlavor || flavors[0];
    showFlavor(flavor);
    select.addEventListener("change", async () => {
      await store.setItem(`${constructor.tag}-flavor`, select.value);
      showFlavor(select.value);
    });
  }
}
safeCustomDefine(PBTAFlavoredSet);
