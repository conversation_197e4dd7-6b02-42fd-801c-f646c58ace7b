import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { PolyDie } from "../playables/dice/die/PolyhedricDie.ts";
import { DiceSet } from "./DiceSet.ts";

export class PBTAGenericSet extends DiceSet {
  static override get label() {
    return `genericPBTA`;
  }
  override get columnNumber(): number {
    return 11;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const groupRollId = (this.constructor as typeof PBTAGenericSet).tag;
    const dice: DieSettings[] = [6, 6].map((size: number) => {
      return {
        type: "die",
        tag: PolyDie.tag,
        name: "d" + size,
        size,
        single: true,
        singleThrowDisabled: true,
        groupRoll: {
          id: groupRollId,
          optional: false,
        },
        span: 3,
      };
    });
    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      span: 5,
      resultConsolidation: { strategy: "size", sum: true },
      resultSort: "descending",
    };
    const elements = [
      ...dice,
      groupRollButton,
    ];
    return elements;
  }
}
safeCustomDefine(PBTAGenericSet);
