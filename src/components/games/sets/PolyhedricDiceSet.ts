import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Coin } from "../playables/dice/die/Coin.ts";
import { CustomDie } from "../playables/dice/die/CustomDie.ts";
import { PolyDie } from "../playables/dice/die/PolyhedricDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-poly`;

export class PolyhedricDiceSet extends DiceSet {
  static override get label(): string {
    return `Polyhedric Dice`;
  }
  override get columnNumber(): number {
    return 3;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const polyDice = [4, 6, 8, 10, 12, 20, 100]
      .map((size: number): DieSettings => {
        return {
          type: "die",
          tag: PolyDie.tag,
          name: "d" + size,
          hasSum: true,
          size: size,
          min: 0,
          groupRoll: {
            id: groupRollId,
          },
          buttonText: {
            text: size.toString(),
          },
        };
      });
    const allDice: DieSettings[] = [
      {
        type: "die",
        tag: Coin.tag,
        name: "coin",
        size: 2,
        min: 0,
        groupRoll: {
          id: groupRollId,
        },
      },
      ...polyDice,
      {
        type: "die",
        tag: CustomDie.tag,
        name: "custom die",
        hasSum: true,
        size: 1,
        min: 0,
        groupRoll: {
          id: groupRollId,
        },
      },
    ];
    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      span: this.columnNumber,
      buttonText: "group roll",
      resultConsolidation: { strategy: "individual" },
    };
    const elements = [
      ...allDice,
      groupRollButton,
    ];
    return elements;
  }
}
safeCustomDefine(PolyhedricDiceSet);
