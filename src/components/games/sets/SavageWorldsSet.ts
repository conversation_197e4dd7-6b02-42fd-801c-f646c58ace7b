import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { PolyDie } from "../playables/dice/die/PolyhedricDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const traitGroupRollId = `${DiceGroupRollButton.tag}-savage-traits`;
const damageGroupRollId = `${DiceGroupRollButton.tag}-savage-damage`;

export class SavageWorldsSet extends DiceSet {
  static override get label(): string {
    return `Savage Worlds™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get columnNumber(): number {
    return 3;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const traitDice: DieSettings[] = [4, 6, 8, 10, 12]
      .map((size: number) => ({
        type: "die",
        tag: PolyDie.tag,
        name: `trait d${size}`,
        size,
        min: 1,
        single: true,
        singleThrowDisabled: true,
        groupRoll: {
          id: traitGroupRollId,
          optional: true,
        },
      }));

    const wildDie: DieSettings = {
      type: "die",
      tag: PolyDie.tag,
      name: "wild d6",
      size: 6,
      min: 1,
      span: 2,
      single: true,
      singleThrowDisabled: true,
      groupRoll: {
        id: traitGroupRollId,
        optional: true,
      },
      label: {
        text: "wild",
        position: "right",
      },
    } as const;
    const traitGroupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: traitGroupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "individual" },
      resultSort: "none",
      span: traitDice.length - wildDie.span!,
    };

    const traitDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `trait`,
      columNumber: 5,
      elements: [...traitDice, wildDie, traitGroupRollButton],
      collapsible: true,
    };
    const damageDice: DieSettings[] = [4, 6, 8, 10, 12]
      .map((size) => ({
        type: "die",
        tag: PolyDie.tag,
        name: `d${size}`,
        size,
        min: 0,
        hasSum: true,
        singleThrowDisabled: true,
        groupRoll: {
          id: damageGroupRollId,
        },
      }));

    const damageGroupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: damageGroupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "size" },
      resultSort: "descending",
      additionalClasses: ["mb-2"],
    };

    const damageDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: "damage",
      columNumber: 3,
      span: 6,
      elements: [...damageDice, damageGroupRollButton],
      collapsible: true,
    };

    return [traitDiceGroup, damageDiceGroup];
  }
}
safeCustomDefine(SavageWorldsSet);
