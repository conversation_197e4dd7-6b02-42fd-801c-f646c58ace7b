import { DiceGroupSettings, DieSettings } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { SixBulletsDie } from "../playables/dice/die/SixBulletsDie.ts";
import { DiceSet } from "./DiceSet.ts";

export class SixBulletsSet extends DiceSet {
  static override get label(): string {
    return `Six Bullets System™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get elements(): (DieSettings | DiceGroupSettings | string)[] {
    return [
      {
        type: "die",
        tag: SixBulletsDie.tag,
        name: "d6",
        size: 6,
        single: true,
      },
    ];
  }
}
safeCustomDefine(SixBulletsSet);
