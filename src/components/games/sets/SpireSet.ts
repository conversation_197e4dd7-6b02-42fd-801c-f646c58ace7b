import { DiceGroupSettings, DieSettings } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { SpireDie } from "../playables/dice/die/SpireDie.ts";
import { DiceSet } from "./DiceSet.ts";

export class SpireSet extends DiceSet {
  static override get label(): string {
    return `Spire™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get columnNumber(): number {
    return 4;
  }
  override get elements(): (DieSettings | DiceGroupSettings | string)[] {
    const stressDice = [3, 6, 8]
      .map((size: number): DieSettings => {
        return {
          type: "die",
          tag: SpireDie.tag,
          name: "d" + size,
          size,
          min: 0,
          single: true,
        };
      });
    return [
      {
        type: "group",
        legend: `action`,
        collapsible: false,
        elements: [{
          type: "die",
          tag: SpireDie.tag,
          name: "d10",
          size: 10,
          min: 1,
          max: 4,
        }],
      },
      {
        type: "group",
        legend: "stress",
        collapsible: false,
        elements: stressDice,
        span: 3,
      },
    ];
  }
}
safeCustomDefine(SpireSet);
