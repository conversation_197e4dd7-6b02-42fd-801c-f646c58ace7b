import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { ValravenDie } from "../playables/dice/die/ValravenDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-valraven`;

export class ValravenSet extends DiceSet {
  static override get label(): string {
    return `Valraven™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const dice: DieSettings[] = [{
      type: "die",
      tag: ValravenDie.tag,
      name: "normal",
      size: 6,
      min: 0,
      groupRoll: {
        id: groupRollId,
      },
      singleThrowDisabled: true,
    }, {
      type: "die",
      tag: ValravenDie.tag,
      name: "advantage",
      size: 6,
      min: 0,
      groupRoll: {
        id: groupRollId,
      },
      singleThrowDisabled: true,
    }, {
      type: "die",
      tag: ValravenDie.tag,
      name: "disadvantage",
      size: 6,
      min: 0,
      groupRoll: {
        id: groupRollId,
      },
      singleThrowDisabled: true,
    }];
    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "individual" },
    };
    return [...dice, groupRollButton];
  }
}
safeCustomDefine(ValravenSet);
