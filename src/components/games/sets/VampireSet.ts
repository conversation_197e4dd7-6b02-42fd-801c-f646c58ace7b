import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { VampireDie } from "../playables/dice/die/VampireDie.ts";
import { VampireNumberDie } from "../playables/dice/die/VampireNumberDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-vampire`;
export class VampireSet extends DiceSet {
  static override get label(): string {
    return `Vampire: The Masquerade`;
  }
  override get columnNumber(): number {
    return 9;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    const specialDice: DieSettings[] = [{
      type: "die",
      tag: VampireDie.tag,
      name: "normal",
      size: 10,
      min: 1,
      span: 2,
      groupRoll: {
        id: groupRollId,
        optional: false,
      },
      singleThrowDisabled: true,
    }, {
      type: "die",
      tag: VampireDie.tag,
      name: "hunger",
      size: 10,
      min: 0,
      span: 2,
      groupRoll: {
        id: groupRollId,
        optional: false,
      },
      singleThrowDisabled: true,
    }];
    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      span: 3,
      resultConsolidation: { strategy: "individual" },
    };
    const d10: DieSettings = {
      type: "die",
      tag: VampireNumberDie.tag,
      name: "d10",
      size: 10,
      span: 2,
    };
    return [
      ...specialDice,
      groupRollButton,
      d10,
    ];
  }
}
safeCustomDefine(VampireSet);
