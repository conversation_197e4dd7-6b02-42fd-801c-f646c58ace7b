import { DiceGroupSettings, DieSettings } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { VampireV20Die } from "../playables/dice/die/VampireV20Die.ts";
import { DiceSet } from "./DiceSet.ts";

export class VampireV20Set extends DiceSet {
  static override get label(): string {
    return `Vampire: The Masquerade (V20)`;
  }
  override get elements(): (DieSettings | DiceGroupSettings | string)[] {
    return [
      {
        type: "die",
        tag: VampireV20Die.tag,
        name: "d10",
        size: 10,
        min: 1,
        max: 20,
      },
    ];
  }
}
safeCustomDefine(VampireV20Set);
