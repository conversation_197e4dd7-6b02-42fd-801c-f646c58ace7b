import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { SettingsButton } from "./buttons/SettingsButton.ts";
import { GenericComponent } from "../core/GenericComponent.ts";
import { ExitRoomButton } from "components/navbar/buttons/ExitRoomButton.ts";
import { CloseSidebarButton } from "components/navbar/buttons/CloseSidebarButton.ts";
import { UserButton } from "components/navbar/buttons/UserButton.ts";
import { InformationButton } from "components/navbar/buttons/InformationButton.ts";
import { WebcamToolsButton } from "components/navbar/buttons/WebcamToolsButton.ts";

export const CLOSE_SIDEBAR_BUTTON_ATTRIBUTE = "has-close-button";

export class NavBar extends GenericComponent {
  connectedCallback() {
    this.classList.add("grid", "p-2", "pl-4");
    this.innerHTML = ` 
            <div class="place-self-start md:place-self-end grid grid-flow-col gap-x-5"> 
              <${CloseSidebarButton.tag}></${CloseSidebarButton.tag}> 
              <${SettingsButton.tag}></${SettingsButton.tag}>
              <${WebcamToolsButton.tag}></${WebcamToolsButton.tag}>
              <${UserButton.tag}></${UserButton.tag}>
              <${InformationButton.tag}></${InformationButton.tag}>
              <${ExitRoomButton.tag}></${ExitRoomButton.tag}>
            </div>
        `;
  }
}
safeCustomDefine(NavBar);
