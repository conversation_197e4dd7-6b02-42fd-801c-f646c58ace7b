import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { SectionSettings } from "./sections/SectionSettings.ts";
import { GenericComponent } from "../core/GenericComponent.ts";
import { SectionExitRoom } from "components/navbar/sections/SectionExitRoom.ts";
import { SectionUser } from "components/navbar/sections/SectionUser.ts";
import { SectionInformation } from "components/navbar/sections/SectionInformation.ts";
import { SectionWebcamTools } from "components/navbar/sections/SectionWebcamTools.ts";

export class NavbarSectionsContainer extends GenericComponent {
  connectedCallback() {
    this.classList.add("block", "px-3");
    this.innerHTML = ` 
            <${SectionSettings.tag}></${SectionSettings.tag}> 
            <${SectionUser.tag}></${SectionUser.tag}>
            <${SectionExitRoom.tag}></${SectionExitRoom.tag}>
            <${SectionInformation.tag}></${SectionInformation.tag}>
            <${SectionWebcamTools.tag}></${SectionWebcamTools.tag}>
        `;
  }
}
safeCustomDefine(NavbarSectionsContainer);
