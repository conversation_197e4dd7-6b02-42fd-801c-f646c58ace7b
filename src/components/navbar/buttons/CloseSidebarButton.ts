import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { SectionSettings } from "../sections/SectionSettings.ts";
import { NavbarButton } from "./NavbarButton.ts";
import { Sidebar } from "../../sidebar/Sidebar.ts";

export class CloseSidebarButton extends NavbarButton {
  override sectionClass = SectionSettings;
  override icon = "arrow-left";
  override ariaLabel = "Close";
  static override class = ["md:hidden"];
  override toggleSection() {
    Sidebar.getInstance<Sidebar>().then((sidebar) => sidebar.hide());
  }
}
safeCustomDefine(CloseSidebarButton);
