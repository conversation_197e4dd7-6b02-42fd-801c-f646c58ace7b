import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NavbarButton } from "components/navbar/buttons/NavbarButton.ts";
import { SectionInformation } from "components/navbar/sections/SectionInformation.ts";

export class InformationButton extends NavbarButton {
  override sectionClass = SectionInformation;
  override icon = "information-outline";
  override ariaLabel = "Information";
}

safeCustomDefine(InformationButton);
