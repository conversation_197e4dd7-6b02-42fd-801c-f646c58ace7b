import { NavbarSection } from "../sections/NavbarSection.ts";
import { GenericComponent } from "../../core/GenericComponent.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { TRANSLATIONS } from "TRANSLATIONS";
import { TranslationController } from "controllers/TranslationController.ts";

export class NavbarButton extends GenericComponent {
  icon = "";

  override ariaLabel: string = "";
  sectionClass: typeof NavbarSection = NavbarSection;
  checkMandatoryParameters: () => void = () => {};

  get section(): NavbarSection {
    const tag = this.sectionClass.tag;
    const section = document.querySelector(tag) as NavbarSection;
    if (!section) {
      throw new Error(`Section ${tag} not found`);
    }
    return section;
  }
  async connectedCallback() {
    this.checkMandatoryParameters();
    const language = await TranslationController.instance.then(
      (controller) => controller.language.then((lang) => lang),
    );
    const tootltipValues =
      TRANSLATIONS[this.ariaLabel as keyof typeof TRANSLATIONS]
        ?.values || {};
    const tooltip = "text" in tootltipValues
      ? tootltipValues.text?.[language]
      : "";
    const toolTipAttribute = tooltip ? `rpgmeet-tooltip="${tooltip}"` : "";
    this.innerHTML = `
            <button 
              type="button" 
              class="grid transform hover:scale-125" aria-label=${this.ariaLabel}
              ${toolTipAttribute}
            >
                <iconify-icon icon="mdi:${this.icon}" height="1.5rem"></iconify-icon>
            </button>
        `;
    this.addEventListener("click", () => {
      this.toggleSection();
    });
    return Promise.resolve();
  }
  toggleSection() {
    this.section.toggle();
  }
}
safeCustomDefine(NavbarButton);
