import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NavbarButton } from "components/navbar/buttons/NavbarButton.ts";
import { SectionWebcamTools } from "components/navbar/sections/SectionWebcamTools.ts";

export class WebcamToolsButton extends NavbarButton {
  override sectionClass = SectionWebcamTools;
  override icon = "webcam";
  override ariaLabel = "Webcam Tools";
}
safeCustomDefine(WebcamToolsButton);
