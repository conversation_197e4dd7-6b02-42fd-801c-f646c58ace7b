import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { assignCheckMandatoryParameters } from "utils/assignCheckMandatoryParameters.ts";
import { Sidebar } from "../../sidebar/Sidebar.ts";
import { GenericComponent } from "../../core/GenericComponent.ts";

const attribute = "navbar-section";

export class NavbarSection extends GenericComponent {
  checkMandatoryParameters: () => void = () => {};
  navbarSectionId: string = "";
  static label: string = "";

  private mandatoryParameters = ["navbarSectionId"];
  set content(htmlContent: string) {
    this.querySelector(`[section-content]`)!.innerHTML = htmlContent;
  }
  override append(element: HTMLElement) {
    this.querySelector(`[section-content]`)!.appendChild(element);
  }
  connectedCallback() {
    this.checkMandatoryParameters();
    this.setAttribute(attribute, this.navbarSectionId);
    this.hiddenClass("add");
    const label = (this.constructor as typeof NavbarSection)["label"];
    this.innerHTML = `
            <fieldset class="p-4 pt-2 border w-full rounded">
                    <legend ${ATTRIBUTE_TRANSLATION_KEY}="${label}"></legend>
                    <div section-content></div>
            </fieldset>
        `;
  }
  show() {
    this.hiddenClass("remove");
  }
  hide() {
    this.hiddenClass("add");
  }
  toggle() {
    this.hiddenClass("toggle");
  }
  private hiddenClass(methodName: "add" | "remove" | "toggle") {
    const sidebar = document.querySelector(Sidebar.tag) as Sidebar;
    const isScrolled = sidebar.scrollTop > 0;
    const sections = document.querySelectorAll<NavbarSection>(`[${attribute}]`);
    sections.forEach((section) => {
      if (section !== this) {
        section.classList.add("hidden");
      } else {
        this.classList[methodName]("hidden");
      }
    });
    if (isScrolled) {
      this.parentElement!.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
      this.classList.remove("hidden");
    }
  }
}
assignCheckMandatoryParameters(NavbarSection);
