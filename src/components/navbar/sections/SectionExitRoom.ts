import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "<PERSON><PERSON><PERSON><PERSON><PERSON>";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NavbarSection } from "./NavbarSection.ts";
import { Room } from "components/room/Room.ts";

export class SectionExitRoom extends NavbarSection {
  static override label = "Exit Room";
  override navbarSectionId = "exit-room";

  override async connectedCallback() {
    super.connectedCallback();
    this.content = `
      <button ${ATTRIBUTE_TRANSLATION_KEY}="Exit Room" class="${GLOBALS.buttonClasses} bg-red-600"></button>
        `;
    this.querySelector("button")?.addEventListener("click", async () => {
      await Room.leave();
    });
  }
}
safeCustomDefine(SectionExitRoom);
