import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NavbarSection } from "components/navbar/sections/NavbarSection.ts";
import { Information } from "core/Information.ts";

export class SectionInformation extends NavbarSection {
  static override label = "Information";
  override navbarSectionId = "information";

  override async connectedCallback() {
    super.connectedCallback();
    this.content = `<${Information.tag}></${Information.tag}>`;
  }
}

safeCustomDefine(SectionInformation);
