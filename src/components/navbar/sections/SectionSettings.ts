import { store } from "store";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NavbarSection } from "./NavbarSection.ts";
import { ResetSettingsButton } from "./settings/ResetSettingsButton.ts";
import { TranslationSelection } from "./settings/TranslationSelection.ts";

export class SectionSettings extends NavbarSection {
  override navbarSectionId: string = "settings";
  static override label = "Settings";

  override async connectedCallback() {
    super.connectedCallback();
    this.content = `
            <style>
                fieldset > legend {
                    text-transform: uppercase;
                }
            </style>
            <fieldset class="mt-1">
                <legend ${ATTRIBUTE_TRANSLATION_KEY}="language"></legend>
                <${TranslationSelection.tag}></${TranslationSelection.tag}>
            </fieldset> 
            <fieldset class="mt-1">
                <legend>DEBUG</legend>
                <div>
                    <input type="checkbox" ${
      await store.getItem("debug") ? "checked" : ""
    } id="input_debug">
                    <label for="input_debug" class="cursor-pointer uppercase" ${ATTRIBUTE_TRANSLATION_KEY}="showDebugMessages"></label>
                </div>
            </fieldset>
            <fieldset class="mt-1">
                <legend ${ATTRIBUTE_TRANSLATION_KEY}="resetSettings"></legend>
                <${ResetSettingsButton.tag}></${ResetSettingsButton.tag}>
            </fieldset>
        `;

    const debugElement = this.querySelector<HTMLInputElement>("#input_debug")!;
    debugElement.addEventListener("change", async () => {
      // document.querySelector(Messages.tag).debug = debugElement.checked;
      await store.setItem(
        "debug",
        debugElement.checked,
      );
    });
  }
}
safeCustomDefine(SectionSettings);
