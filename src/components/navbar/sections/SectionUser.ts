import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NavbarSection } from "./NavbarSection.ts";
import { Logout } from "core/Logout.ts";
import { Nickname } from "components/room/Nickname.ts";

export class SectionUser extends NavbarSection {
  static override label = "User";
  override navbarSectionId = "user";

  override async connectedCallback() {
    await super.connectedCallback();
    this.content = `
      <div class="grid gap-2">
        <${Nickname.tag}></${Nickname.tag}>
        <${Logout.tag}></${Logout.tag}>
      </div>
      `;
  }
}
safeCustomDefine(SectionUser);
