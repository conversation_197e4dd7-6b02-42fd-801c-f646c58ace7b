import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NavbarSection } from "components/navbar/sections/NavbarSection.ts";
import { OverlaySettingMainSection } from "components/navbar/sections/overlay/sections/OverlaySettingMainSection.ts";
import { OverlaySettingAvatarSection } from "components/navbar/sections/overlay/sections/OverlaySettingAvatarSection.ts";
import { OverlaySettingTableSection } from "components/navbar/sections/overlay/sections/OverlaySettingTableSection.ts";
import { OverlaySettingTitleSection } from "components/navbar/sections/overlay/sections/OverlaySettingTitleSection.ts";

export class SectionWebcamTools extends NavbarSection {
  static override label = "Webcam Tools";
  override navbarSectionId = "webcam-tools";

  override async connectedCallback(): Promise<void> {
    super.connectedCallback();
    this.content = `
            <p ${ATTRIBUTE_TRANSLATION_KEY}="overlayAlert"></p>
            <${OverlaySettingMainSection.tag}></${OverlaySettingMainSection.tag}>
            <${OverlaySettingTitleSection.tag}></${OverlaySettingTitleSection.tag}>
            <${OverlaySettingAvatarSection.tag}></${OverlaySettingAvatarSection.tag}>
            <${OverlaySettingTableSection.tag}></${OverlaySettingTableSection.tag}>
        `;
  }
}
safeCustomDefine(SectionWebcamTools);
