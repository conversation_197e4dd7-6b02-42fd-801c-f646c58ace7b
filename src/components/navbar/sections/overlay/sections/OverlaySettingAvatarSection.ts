import { GenericComponent } from "core/GenericComponent.ts";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OverlayAvatarInput } from "components/navbar/sections/overlay/setting/input/input-types/OverlayAvatarInput.ts";

export class OverlaySettingAvatarSection extends GenericComponent {
  async connectedCallback(): Promise<void> {
    this.innerHTML = `
            <fieldset class="py-2 px-1 border rounded">
                <legend 
                    class="uppercase font-bold" 
                >avatar</legend>
                <div>
                    <p ${ATTRIBUTE_TRANSLATION_KEY}="maxOverlayFileSize"></p>
                    <${OverlayAvatarInput.tag}></${OverlayAvatarInput.tag}>
                </div>
            </fieldset>
        `;
  }
}
safeCustomDefine(OverlaySettingAvatarSection);
