import { OverlaySettingSectionConfig } from "types/OverlaySettings.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OverlaySettingSection } from "components/navbar/sections/overlay/sections/OverlaySettingSection.ts";

export class OverlaySettingMainSection extends OverlaySettingSection {
  override get config(): OverlaySettingSectionConfig {
    return {
      title: "overlay",
      settings: [
        {
          name: "active",
          label: "active",
          colSpan: 6,
          inputType: "checkbox",
          translatable: true,
        },
        {
          name: "mirror",
          label: "mirror",
          colSpan: 6,
          inputType: "checkbox",
          translatable: true,
        },
      ],
    };
  }
}
safeCustomDefine(OverlaySettingMainSection);
