import { GenericComponent } from "core/GenericComponent.ts";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { OverlaySettingSectionConfig } from "types/OverlaySettings.ts";
import { OverlaySetting } from "components/navbar/sections/overlay/setting/OverlaySetting.ts";

export class OverlaySettingSection extends GenericComponent {
  get config(): OverlaySettingSectionConfig {
    return {
      title: "",
      settings: [],
    };
  }
  async connectedCallback(): Promise<void> {
    const { title, settings } = this.config;
    this.innerHTML = `
            <fieldset class="py-2 px-1 border rounded">
                <legend 
                    ${ATTRIBUTE_TRANSLATION_KEY}="${title}"
                    class="uppercase font-bold"
                ></legend>
                <div class="grid grid-cols-12">
                    ${
      settings.map((setting) => `
                        <${OverlaySetting.tag}
                            name="${setting.name}"
                            col-span="${setting.colSpan || "12"}"
                            label="${setting.label}"
                            label-columns="${setting.labelColumns}"
                            input-type="${setting.inputType}"
                            ${setting.translatable ? "translatable" : ""}
                            ${setting.min ? `min="${setting.min}"` : ""}
                            ${setting.max ? `max="${setting.max}"` : ""}
                            ${setting.step ? `step="${setting.step}"` : ""}
                            ${
        setting["default-value"]
          ? `default-value="${setting["default-value"]}"`
          : ""
      }
                        ></${OverlaySetting.tag}>
                    `).join("")
    }
                </div>
            </fieldset>
        `;
  }
}
