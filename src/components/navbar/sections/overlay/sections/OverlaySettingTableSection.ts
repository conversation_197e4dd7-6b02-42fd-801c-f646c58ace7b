import { GenericComponent } from "core/GenericComponent.ts";
import { OVERLAY_TABLE_SETTINGS } from "GLO<PERSON>LS";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OverlayNumberInput } from "components/navbar/sections/overlay/setting/input/input-types/OverlayNumberInput.ts";
import { OverlaySetting } from "components/navbar/sections/overlay/setting/OverlaySetting.ts";

export class OverlaySettingTableSection extends GenericComponent {
  async connectedCallback(): Promise<void> {
    const rowsColsInputColSpan = 5;
    const fontSizeInputColSpan = 12 - (rowsColsInputColSpan * 2);
    const labelColumns = 8;
    const tableFontSizeMin = 12;
    const tableFontSizeMax = 24;
    const fontSizeLabelColumns = 6;
    this.innerHTML = `
            <fieldset class="py-2 px-1 border rounded">
                <legend
                    class="uppercase font-bold"
                    ${ATTRIBUTE_TRANSLATION_KEY}="infoTable"
                ></legend>
                <div class="grid grid-flow-col gap-1">
                    <${OverlaySetting.tag}
                        rowscols_input
                            name="rows"
                            col-span="${rowsColsInputColSpan}"
                            label="rows"
                            label-columns="${labelColumns}"
                            input-type="number"
                            translatable
                            min="${OVERLAY_TABLE_SETTINGS.rows.min}"
                            max="${OVERLAY_TABLE_SETTINGS.rows.max}"
                    ></${OverlaySetting.tag}>
                    <${OverlaySetting.tag}
                            rowscols_input
                            name="columns"
                            col-span="${rowsColsInputColSpan}"
                            label="columns"
                            label-columns="${labelColumns}"
                            input-type="number"
                            translatable
                            min="${OVERLAY_TABLE_SETTINGS.columns.min}"
                            max="${OVERLAY_TABLE_SETTINGS.columns.max}"
                    ></${OverlaySetting.tag}>
                    <${OverlaySetting.tag}
                            name="tableFontSize"
                            col-span="${fontSizeInputColSpan}"
                            label="A↕"
                            label-columns="${fontSizeLabelColumns}"
                            input-type="number"
                            min="${tableFontSizeMin}"
                            max="${tableFontSizeMax}"
                    ></${OverlaySetting.tag}>
                </div>
                <div table class="grid"></div>
            </fieldset>
        `;
    this.querySelectorAll("[rowscols_input]").forEach(
      (input) => {
        input.addEventListener("change", () => {
          this.generateTable();
        });
      },
    );
  }
  private generateTable() {
    const table = this.querySelector<HTMLTableElement>("[table]")!;
    const rowsInput = this.querySelector<OverlayNumberInput>('[name="rows"]')!;
    const rowsNumber: number = rowsInput.value;
    const columnsInput = this.querySelector<OverlayNumberInput>(
      '[name="columns"]',
    )!;
    const columnsNumber: number = columnsInput.value;
    table.innerHTML = "";
    if (!rowsNumber || !columnsNumber) {
      return;
    }
    table.className =
      `grid grid-cols-${columnsNumber} grid-rows-${rowsNumber} gap-2`;
    for (let row = 0; row < rowsNumber; row++) {
      for (let col = 0; col < columnsNumber; col++) {
        const cell = document.createElement("div");
        cell.innerHTML = `
                    <${OverlaySetting.tag}
                        name="table_${row}_${col}"
                        input-type="text"
                    ></${OverlaySetting.tag}>
                `;
        table.appendChild(cell);
      }
    }
  }
}
safeCustomDefine(OverlaySettingTableSection);
