import { OverlaySettingSectionConfig } from "types/OverlaySettings.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OverlaySettingSection } from "components/navbar/sections/overlay/sections/OverlaySettingSection.ts";

export class OverlaySettingTitleSection extends OverlaySettingSection {
  override get config(): OverlaySettingSectionConfig {
    const settingColSpan = 9;
    const fontSizeColSpan = 12 - settingColSpan;
    const settingLabelColSpan = 3;
    const fontSizeLabelColSpan = 6;
    return {
      title: "title",
      settings: [
        {
          name: "title",
          label: "title",
          colSpan: settingColSpan,
          labelColumns: settingLabelColSpan,
          inputType: "text",
          translatable: true,
        },
        {
          name: "titleFontSize",
          label: "A↕",
          colSpan: fontSizeColSpan,
          labelColumns: fontSizeLabelColSpan,
          inputType: "number",
          min: 15,
          max: 45,
          step: 1,
          "default-value": 15,
        },
        {
          name: "subtitle",
          label: "subtitle",
          colSpan: settingColSpan,
          labelColumns: settingLabelColSpan,
          inputType: "text",
          translatable: true,
        },
        {
          name: "subtitleFontSize",
          label: "A↕",
          colSpan: fontSizeColSpan,
          labelColumns: fontSizeLabelColSpan,
          inputType: "number",
          min: 15,
          max: 45,
          step: 1,
          "default-value": 15,
        },
      ],
    };
  }
}
safeCustomDefine(OverlaySettingTitleSection);
