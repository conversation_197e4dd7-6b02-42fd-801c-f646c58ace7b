import { GenericComponent } from "core/GenericComponent.ts";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OverlayFilter } from "components/navbar/sections/overlay/setting/input/OverlayFilter.ts";

export class OverlaySettingVideoFilterSection extends GenericComponent {
  async connectedCallback() {
    const videoFilters = [
      {
        type: "blur",
        min: 0,
        max: 5,
        default: 0,
      },
      {
        type: "brightness",
        min: 0,
        max: 100,
        default: 100,
      },
      {
        type: "contrast",
        min: 0,
        max: 100,
        default: 100,
      },
      {
        type: "grayscale",
        min: 0,
        max: 100,
        default: 0,
      },
      {
        type: "invert",
        min: 0,
        max: 100,
        default: 0,
      },
      {
        type: "saturate",
        min: 0,
        max: 100,
        default: 100,
      },
      {
        type: "sepia",
        min: 0,
        max: 100,
        default: 0,
      },
    ];
    const videoFiltersHTML = videoFilters.map((videoFilter) => {
      return `<${OverlayFilter.tag} 
                        overlay-filter-type="${videoFilter.type}" 
                        overlay-filter-min="${videoFilter.min}" 
                        overlay-filter-max="${videoFilter.max}"
                        overlay-filter-default="${videoFilter.default}"
                    ></${OverlayFilter.tag}>`;
    }).join("");
    this.innerHTML = `<fieldset class="py-2">
        <legend 
            ${ATTRIBUTE_TRANSLATION_KEY}="video filters"
            class="uppercase font-bold"
        ></legend>
        <div class="grid grid-cols-2">
            ${videoFiltersHTML}
        </div>
    </fieldset>`;
  }
}
safeCustomDefine(OverlaySettingVideoFilterSection);
