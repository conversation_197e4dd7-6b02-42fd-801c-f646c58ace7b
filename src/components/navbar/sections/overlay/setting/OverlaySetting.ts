import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { OverlaySettingConfig } from "types/OverlaySettings.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OverlayAvatarInput } from "components/navbar/sections/overlay/setting/input/input-types/OverlayAvatarInput.ts";
import { OverlayCheckboxInput } from "components/navbar/sections/overlay/setting/input/input-types/OverlayCheckboxInput.ts";
import { OverlayBaseInput } from "components/navbar/sections/overlay/setting/input/OverlayBaseInput.ts";
import { OverlayNumberInput } from "components/navbar/sections/overlay/setting/input/input-types/OverlayNumberInput.ts";
import { OverlayTextInput } from "components/navbar/sections/overlay/setting/input/input-types/OverlayTextInput.ts";
import { GenericComponent } from "core/GenericComponent.ts";

export class OverlaySetting extends GenericComponent {
  get name(): string {
    return this.getAttribute("name") || "";
  }
  get label(): string {
    return this.getAttribute("label") || "";
  }
  get labelCols(): number {
    const hasLabel = this.hasAttribute("label");
    if (!hasLabel) {
      return 0;
    }
    return Number(this.getAttribute("label-columns")) || 1;
  }
  get inputCols(): number {
    return 12 - this.labelCols;
  }
  get inputType(): string {
    return this.getAttribute("input-type") || "text";
  }
  get translatable(): boolean {
    return this.hasAttribute("translatable");
  }
  get inputElement(): OverlayBaseInput {
    return this.lastElementChild as OverlayBaseInput;
  }
  get value(): OverlayBaseInput["value"] {
    return this.inputElement.value;
  }
  async connectedCallback(): Promise<void> {
    const inputTagMapping = {
      "text": OverlayTextInput.tag,
      "number": OverlayNumberInput.tag,
      "checkbox": OverlayCheckboxInput.tag,
      "avatar": OverlayAvatarInput.tag,
    };
    const colSpan = this.getAttribute("col-span");
    const colSpanClass = `col-span-${colSpan}`;
    this.className = `${colSpanClass} grid grid-cols-12 gap-1`;
    const labelColSpan = this.labelCols ? `col-span-${this.labelCols}` : "";
    const labelClassName = `${labelColSpan} text-center`;
    const labelHTML = this.label
      ? `
                <label 
                    for="${this.name}"  
                    class="${labelClassName}" 
                    ${
        this.translatable ? `${ATTRIBUTE_TRANSLATION_KEY}=${this.label}` : ""
      }
                >${this.translatable ? "" : this.label}</label>
            `
      : "";
    const stringAttribute = (attribute: keyof OverlaySettingConfig) =>
      this.hasAttribute(attribute)
        ? `${attribute}="${this.getAttribute(attribute)}"`
        : "";
    const inputTag =
      inputTagMapping[this.inputType as keyof typeof inputTagMapping];
    if (!inputTag) {
      throw new Error(`Input type ${this.inputType} not found`);
    }
    const inputColSpan = this.inputCols ? `col-span-${this.inputCols}` : "";
    const inputClassName = `${inputColSpan}`;
    const inputHTML = `
            <${inputTag}
                input-type="${this.inputType}"
                name="${this.name}"
                class="${inputClassName}"
                ${stringAttribute("min")}
                ${stringAttribute("max")}
                ${stringAttribute("step")}
                ${stringAttribute("default-value")}
            ></${inputTag}>`;
    this.innerHTML = `
            ${labelHTML}
            ${inputHTML}
        `;
    this.inputElement.addEventListener("change", () => {
      this.dispatchEvent(new Event("change"));
    });
    if (this.inputType === "checkbox") {
      const label = this.querySelector("label")!;
      label.classList.add("cursor-pointer");
      label.addEventListener(
        "click",
        (): void => this.querySelector("input")?.click(),
      );
    }
  }
}
safeCustomDefine(OverlaySetting);
