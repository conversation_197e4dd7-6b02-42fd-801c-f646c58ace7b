import { OverlayPersistedInput } from "components/navbar/sections/overlay/setting/input/OverlayPersistedInput.ts";

export class OverlayBaseInput extends OverlayPersistedInput {
  get inputType(): string {
    return this.getAttribute("input-type") || "text";
  }
  get content(): string {
    return `<input
                    type="${this.inputType}"                    
                    class="w-full"
                >`;
  }
  async connectedCallback(): Promise<void> {
    this.innerHTML = this.content;
  }
}
