import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OverlayPersistedInput } from "components/navbar/sections/overlay/setting/input/OverlayPersistedInput.ts";

export const attributeIdentifier: string = "overlay-filter";

export class OverlayFilter extends OverlayPersistedInput {
  get mandatoryAttributes(): string[] {
    return ["type", "min", "max", "default"];
  }
  get type(): string {
    return this.getAttribute(`${attributeIdentifier}-type`) || "";
  }
  get defaultValue(): string {
    return this.getAttribute(`${attributeIdentifier}-default`) || "";
  }
  async connectedCallback() {
    const properties = this.mandatoryAttributes.map(
      this._mapMandatoryAttributes,
    );
    const missingProperties = properties.filter((property: string): boolean =>
      !this.getAttribute(property)
    );
    if (missingProperties.length > 0) {
      throw new Error(
        `Missing properties for ${this.tag}:${
          missingProperties.join(", ")
        }. Specify them as attributes.`,
      );
    }
    this.innerHTML = `
            <p class="text-center" ${ATTRIBUTE_TRANSLATION_KEY}="${this.type}"></p>
            <div class="grid grid-cols-5">
                <input
                    class="col-span-4"
                    type="range"
                    min="${this.getAttribute(`${attributeIdentifier}-min`)}"
                    max="${this.getAttribute(`${attributeIdentifier}-max`)}"
                > </input>
                <button 
                    class="col-span-1 rounded transform hover:scale-110 focus:scale-100 hover:bg-blue-600 text-white"
                >✖️</button>
            </div>
            `;
    this.inputElement.addEventListener("change", async () => {
      await this.handleChange();
    });
    this.querySelector("button")?.addEventListener("click", async () => {
      await this.reset();
    });
  }
  async reset() {
    await this.setPersistedValue(this.defaultValue);
  }
  _mapMandatoryAttributes(mandatoryAttribute: string): string {
    return `${attributeIdentifier}-${
      mandatoryAttribute.replace(`${attributeIdentifier}-`, "")
    }`;
  }
}
safeCustomDefine(OverlayFilter);
