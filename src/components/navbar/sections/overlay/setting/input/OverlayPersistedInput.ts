import { OverlaySettings } from "types/OverlaySettings.ts";
import { updateLocalforageItemProperty } from "utils/updateLocalforageItemProperty.ts";
import { WEBCAM_OVERLAY_LOCALFORAGE_ID } from "GLO<PERSON>LS";
import { GenericComponent } from "core/GenericComponent.ts";
import { UserDetailsController } from "controllers/UserDetailsController.ts";
import { store } from "store";
import { Videocall } from "components/room/Videocall.ts";
import { devLog } from "utils/devLog.ts";

let isFirstRun = true;

export class OverlayPersistedInput extends GenericComponent {
  get name(): keyof OverlaySettings {
    return this.getAttribute("name") as keyof OverlaySettings;
  }
  get inputElement(): HTMLInputElement {
    return this.querySelector("input") as HTMLInputElement;
  }
  get value(): string | number | boolean {
    const input = this.inputElement as HTMLInputElement;
    return input.value;
  }
  set value(value: string) {
    const input = this.inputElement as HTMLInputElement;
    input.value = value;
  }
  async handleChange() {
    await this.setPersistedValue(this.value);
  }
  async getPersistedValue() {
    const persistedSettings = await store.getItem<OverlaySettings>(
      WEBCAM_OVERLAY_LOCALFORAGE_ID,
    );
    if (!persistedSettings) return undefined;

    const persistedValue = persistedSettings?.[this.name];
    if (isFirstRun) {
      isFirstRun = false;
      setTimeout(async () => {
        await sendOverlayUpdate(persistedSettings);
      }, 2000);
    }
    return persistedValue;
  }
  async setPersistedValue(
    value: string | number | ArrayBuffer | string[][] | boolean | null,
  ): Promise<void> {
    await updateLocalforageItemProperty(
      WEBCAM_OVERLAY_LOCALFORAGE_ID,
      this.name,
      value,
    );
    const overlaySettings = await store.getItem<OverlaySettings>(
      WEBCAM_OVERLAY_LOCALFORAGE_ID,
    ).then((settings) => {
      if (!settings) {
        return {
          active: false,
          title: "",
          titleFontSize: 1,
          subtitle: "",
          subtitleFontSize: 1,
          avatar: "",
          tableFontSize: 1,
        };
      }
      return settings as OverlaySettings;
    });
    await sendOverlayUpdate(overlaySettings);
  }
}

const sendOverlayUpdate = async (overlaySettings: OverlaySettings) => {
  devLog({ sending: "overlay", overlaySettings }, { action: "warn" });
  const nickname = await UserDetailsController.nickname;
  const videocall = await Videocall.getInstance<Videocall>();
  await videocall.sendMessage({
    type: "overlay",
    nickname,
    overlaySettings,
  });
};
