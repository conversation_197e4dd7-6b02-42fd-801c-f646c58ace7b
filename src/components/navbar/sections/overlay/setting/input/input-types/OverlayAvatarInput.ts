import { TranslationController } from "controllers/TranslationController.ts";
import { MAX_OVERLAY_FILE_SIZE_MB } from "GLOBALS";
import { ATTRIBUTE_TRANSLATION_KEY, TRANSLATIONS } from "TRANSLATIONS";
import { OverlaySettings } from "types/OverlaySettings.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OverlayBaseInput } from "components/navbar/sections/overlay/setting/input/OverlayBaseInput.ts";
import { Notify } from "notiflix";

export class OverlayAvatarInput extends OverlayBaseInput {
  override get name(): keyof OverlaySettings {
    return "avatar" as keyof OverlaySettings;
  }
  override get content(): string {
    const buttonClasses: string =
      " p-1 border rounded transform hover:bg-gray-700 text-white";
    return `
            <input 
                name="avatar" 
                type="file"
                accept="image/png, image/gif, image/jpeg" 
                style="display:none"
            >
            <div class="grid grid-cols-2 gap-2">
                <button 
                    avatar_choose 
                    class="${buttonClasses}" 
                    ${ATTRIBUTE_TRANSLATION_KEY}="choose"
                ></button>
                <button 
                    file_clear 
                    class="${buttonClasses}" 
                    ${ATTRIBUTE_TRANSLATION_KEY}="remove"
                ></button>
            </div>
        `;
  }
  override async connectedCallback(): Promise<void> {
    await super.connectedCallback();
    const input = this.querySelector("input");
    if (!input) return;

    const selectorButton = this.querySelector("[avatar_choose]");
    if (!selectorButton) return;

    const clearButton = this.querySelector("[file_clear]");
    if (!clearButton) return;

    selectorButton.addEventListener("click", () => input.click());
    input.addEventListener("change", async () => {
      const fileReader = new FileReader();
      fileReader.onload = async (): Promise<void> => {
        if (!fileReader.result) return;
        await this.setPersistedValue(fileReader.result);
      };
      if (input.files?.[0]) {
        if (!input.files[0].type.match(/image.*/)) {
          await showError("fileFormatNotSupported");
          return;
        }
        if (input.files[0].size > MAX_OVERLAY_FILE_SIZE_MB * 1024 * 1024) {
          await showError("fileTooBig");
          return;
        }
        fileReader.readAsDataURL(input.files[0]);
      }
    });
    clearButton.addEventListener("click", async (): Promise<void> => {
      await this.setPersistedValue(null);
    });
  }
}
safeCustomDefine(OverlayAvatarInput);

const showError = async (
  translationKey: keyof typeof TRANSLATIONS,
): Promise<void> => {
  const translationController = await TranslationController.instance;
  const title = await translationController.getTranslation("error");
  const text = await translationController.getTranslation(translationKey);
  Notify.failure(`${title}: ${text}`);
  return;
};
