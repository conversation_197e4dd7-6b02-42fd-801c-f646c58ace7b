import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OverlayRegularInput } from "components/navbar/sections/overlay/setting/input/input-types/OverlayRegularInput.ts";

export class OverlayCheckboxInput extends OverlayRegularInput {
  override async getPersistedValue(): Promise<boolean> {
    return !!(await super.getPersistedValue());
  }
  override async connectedCallback(): Promise<void> {
    await super.connectedCallback();
    const input = this.inputElement;
    const persistedValue: boolean = await this.getPersistedValue();
    input.checked = persistedValue;
  }
  override async handleChange() {
    const input = this.inputElement;
    await this.setPersistedValue(input.checked);
  }
}
safeCustomDefine(OverlayCheckboxInput);
