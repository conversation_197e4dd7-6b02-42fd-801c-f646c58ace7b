import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OverlayTextInput } from "components/navbar/sections/overlay/setting/input/input-types/OverlayTextInput.ts";

const DEFAULT_ATTRIBUTES = {
  min: 0,
  max: "",
  step: 1,
  value: 0,
} as const;
export class OverlayNumberInput extends OverlayTextInput {
  get isValid(): boolean {
    return this.checkValidity();
  }
  get min(): number {
    return Number(this.getAttribute("min") || DEFAULT_ATTRIBUTES.min);
  }
  get max(): number {
    return Number(this.getAttribute("max") || DEFAULT_ATTRIBUTES.max);
  }
  get step(): number {
    return Number(this.getAttribute("step") || DEFAULT_ATTRIBUTES.step);
  }
  override get content(): string {
    return `<input
                    type="${this.inputType}"                    
                    class="w-full"
                    min="${this.min}"
                    max="${this.max}"
                    step="${this.step}"
                    required
                >`;
  }
  override get value(): number {
    const defaultValue = this.getAttribute("default-value");
    return Number(
      this.inputElement.value || defaultValue || DEFAULT_ATTRIBUTES.value,
    );
  }
  override async connectedCallback(): Promise<void> {
    await super.connectedCallback();
    const input = this.inputElement as HTMLInputElement;
    input.value = (this.value || this.min).toString();
  }
  override async handleChange() {
    if (!this.isValid) {
      this.inputElement.value = (await this.getPersistedValue().then((value) =>
        value || this.min
      )).toString();
      return;
    }
    await super.handleChange();
  }
  private checkValidity(): boolean {
    const input: HTMLInputElement = this.inputElement;
    input.setCustomValidity("");
    const isValid: boolean = input.checkValidity();
    if (!isValid || !input.value) {
      input.setCustomValidity(`min ${this.min}, max ${this.max}`);
      input.reportValidity();
      return false;
    }
    return true;
  }
}
safeCustomDefine(OverlayNumberInput);
