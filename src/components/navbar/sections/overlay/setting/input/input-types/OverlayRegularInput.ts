import { OverlayBaseInput } from "components/navbar/sections/overlay/setting/input/OverlayBaseInput.ts";

export class OverlayRegularInput extends OverlayBaseInput {
  override async connectedCallback(): Promise<void> {
    await super.connectedCallback();
    const input = this.inputElement as HTMLInputElement;
    input.addEventListener("change", async () => {
      await this.handleChange();
    });
  }
}
