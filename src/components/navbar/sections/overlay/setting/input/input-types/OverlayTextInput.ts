import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OverlayRegularInput } from "components/navbar/sections/overlay/setting/input/input-types/OverlayRegularInput.ts";

export class OverlayTextInput extends OverlayRegularInput {
  override async connectedCallback(): Promise<void> {
    await super.connectedCallback();

    const input = this.inputElement as HTMLInputElement;
    const persistedValue = await this.getPersistedValue();
    const thisValue = this.value;
    const value = persistedValue ?? thisValue;
    const stringValue = value.toString();
    input.value = stringValue;
    this.dispatchEvent(new Event("change"));
    input.addEventListener("keyup", async () => {
      await this.handleChange();
    });
  }
}
safeCustomDefine(OverlayTextInput);
