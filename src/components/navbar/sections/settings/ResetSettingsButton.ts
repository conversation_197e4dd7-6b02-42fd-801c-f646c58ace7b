import { store } from "store";
import { TranslationController } from "controllers/TranslationController.ts";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "GL<PERSON><PERSON>LS";
import { ATTRIBUTE_TRANSLATION_KEY, TRANSLATIONS } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { GenericComponent } from "../../../core/GenericComponent.ts";
import { Confirm } from "notiflix";

export class ResetSettingsButton extends GenericComponent {
  async connectedCallback() {
    this.innerHTML = `
            <button 
              class="${GLOBALS.buttonClasses} bg-red-600">
                <span 
                  ${ATTRIBUTE_TRANSLATION_KEY}="reset"
                >
                </span>
            </button>
        `;
    this.querySelector("button")!.addEventListener("click", async () => {
      const translationController = await TranslationController.instance;
      const language = await translationController.language;
      Confirm.show(
        TRANSLATIONS.confirm.values.text[language],
        TRANSLATIONS.confirmResetSettingsMessage.values.text[language],
        TRANSLATIONS.confirm.values.text[language],
        TRANSLATIONS.cancel.values.text[language],
        async () => {
          await store.clear();
          location.reload();
        },
        () => {},
        {
          okButtonBackground: "#dc2626",
          cancelButtonBackground: "#3085d6",
        },
      );
    });
  }
}
safeCustomDefine(ResetSettingsButton);
