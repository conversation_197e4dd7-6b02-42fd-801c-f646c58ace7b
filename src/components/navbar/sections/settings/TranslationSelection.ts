import { store } from "store";
import { TranslationController } from "controllers/TranslationController.ts";
import { languageNames, languages, TranslationLanguage } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { GenericComponent } from "../../../core/GenericComponent.ts";

export class TranslationSelection extends GenericComponent {
  async connectedCallback() {
    await this.setLanguageFromLocale();
    const translationController = await TranslationController.instance;
    const storedLanguage: TranslationLanguage = await translationController
      .language;
    this.innerHTML = `
            <select class="cursor-pointer uppercase text-black">
                ${
      languages.map((language: TranslationLanguage): string => `
                    <option value=${language} ${
        language === storedLanguage ? "selected" : ""
      } class="uppercase">
                        ${languageNames[language]}
                    </option>
                `).join("")
    }
            </select>
        `;
    this.querySelector("select")!.addEventListener("change", () => {
      const selectedLanguage: TranslationLanguage = this.querySelector(
        "select",
      )!.value as TranslationLanguage;
      translationController.setLanguage(selectedLanguage);
    });
    translationController.setLanguage(storedLanguage);
  }
  async setLanguageFromLocale(): Promise<boolean> {
    const autoDetectFirstRun = await store.getItem(
      `auto-detect-first-run`,
    );
    if (autoDetectFirstRun) {
      return true;
    }
    const locale = globalThis.navigator.language;
    const detectedLanguage = locale.substring(0, 2) as TranslationLanguage;
    if (languages.includes(detectedLanguage)) {
      const translationController = await TranslationController.instance;
      translationController.setLanguage(detectedLanguage);
      store.setItem(`language`, detectedLanguage);
      store.setItem(`auto-detect-first-run`, true);
    }
    return true;
  }
}
safeCustomDefine(TranslationSelection);
