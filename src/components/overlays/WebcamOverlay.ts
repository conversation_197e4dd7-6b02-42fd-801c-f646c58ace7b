import { GL<PERSON><PERSON><PERSON> } from "GLO<PERSON>LS";
import { GenericComponent } from "core/GenericComponent.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";

const instances: {
  [nickname: string]: WebcamOverlay;
} = {};

export class WebcamOverlay extends GenericComponent {
  static override tag = `${GLOBALS.PREFIX}-webcam-overlay`;

  get nickname(): string {
    return this.getAttribute("nickname")!;
  }
  set nickname(nickname: string) {
    this.setAttribute("nickname", nickname);
  }

  static async getUserOverlay(nickname: string): Promise<WebcamOverlay> {
    if (instances[nickname]) return instances[nickname];

    const newWebcamOverlay = new WebcamOverlay();
    newWebcamOverlay.nickname = nickname;
    instances[nickname] = newWebcamOverlay;

    return newWebcamOverlay;
  }
}
safeCustomDefine(WebcamOverlay);
