import { store } from "store";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { Login } from "core/Login.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { GenericComponent } from "core/GenericComponent.ts";
import { Videocall } from "components/room/Videocall.ts";

export class Nickname extends GenericComponent {
  static async getNickname() {
    const stored = await store.getItem<string>("nickname");
    if (stored) return stored;
    const email = await Login.email;
    return email;
  }
  private static async setNickname(nickname: string) {
    await store.setItem("nickname", nickname);
    const videocall = await Videocall.getInstance<Videocall>();
    await videocall.sendNickname(nickname);
  }

  async connectedCallback() {
    const existingNickname = await Nickname.getNickname();
    this.innerHTML = `
      <label class="text-center" ${ATTRIBUTE_TRANSLATION_KEY}="nickname"></label>
      <input nickname_input class="text-center" type="text" min="3" value="${existingNickname}">
      `;

    this.querySelector<HTMLInputElement>("[nickname_input]")?.addEventListener(
      "change",
      async () => {
        const nickname = this.querySelector<HTMLInputElement>(
          "[nickname_input]",
        )!.value!;
        await Nickname.setNickname(nickname);
      },
    );

    this.querySelector<HTMLInputElement>("[nickname_input]")?.addEventListener(
      "keydown",
      async (e) => {
        if (e.key === "Enter") {
          const nickname = this.querySelector<HTMLInputElement>(
            "[nickname_input]",
          )!.value!;
          await Nickname.setNickname(nickname);
        }
      },
    );
  }
}
safeCustomDefine(Nickname);
