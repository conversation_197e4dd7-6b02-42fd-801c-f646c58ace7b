import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Singleton } from "../core/Singleton.ts";
import { Videocall } from "./Videocall.ts";
import { Sidebar } from "../sidebar/Sidebar.ts";
import { App } from "core/App.ts";
import { Confirm } from "notiflix";
import { TranslationController } from "controllers/TranslationController.ts";
import { TRANSLATIONS } from "TRANSLATIONS";

export class Room extends Singleton {
  static override class = [
    "w-screen",
    "h-full",
    "grid",
    "md:grid-cols-12",
  ];
  connectedCallback() {
    this.innerHTML = `
        <${Videocall.tag}  
            room-name="${this.getAttribute("room-name")}"
            class="absolute md:static top-0 md:top-auto left-0 md:left-auto w-full h-full md:h-screen md:block md:col-span-9"
        ></${Videocall.tag}>
        <${Sidebar.tag}
        class="absolute md:static top-0 md:top-auto left-0 md:left-auto w-full h-full md:h-screen z-50 md:z-auto bg-gradient-to-tr from-red-900 to-black bg-opacity-85 md:bg-opacity-100 md:bg-transparent md:col-span-3 text-base grid-rows-12"
    ></${Sidebar.tag}>
     
    `;
  }
  static getId() {
    return Room.getInstance<Room>().then((room) =>
      room.getAttribute("room-name")!
    );
  }
  static async leave() {
    const translationController = await TranslationController.instance;
    const language = await translationController.language;
    Confirm.show(
      TRANSLATIONS["Exit Room"].values.text[language],
      "",
      TRANSLATIONS.confirm.values.text[language],
      TRANSLATIONS.cancel.values.text[language],
      async () => await App.getInstance<App>().then((app) => app.reset()),
      () => {},
      {
        okButtonBackground: "#dc2626",
        cancelButtonBackground: "#3085d6",
      },
    );
  }
}
safeCustomDefine(Room);
