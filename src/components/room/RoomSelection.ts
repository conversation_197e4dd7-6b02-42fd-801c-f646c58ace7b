import { GenericComponent } from "../core/GenericComponent.ts";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "GLO<PERSON>LS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { animateCSS } from "utils/animateCss.ts";
import { App } from "../core/App.ts";
import { ATTRIBUTE_TRANSLATION_KEY, TRANSLATIONS } from "TRANSLATIONS";
import { TranslationController } from "controllers/TranslationController.ts";
import { store } from "store";
import { Logout } from "core/Logout.ts";
import { Notify } from "notiflix";
import { Nickname } from "components/room/Nickname.ts";

export class RoomSelection extends GenericComponent {
  async connectedCallback() {
    const urlParams = new URLSearchParams(globalThis.location.search);
    const roomName = urlParams.get("room");
    if (roomName) {
      await this.showRoom(roomName);
      return;
    }
    const lastRoom = await store.getItem<string | null>("lastroom") ||
      "";
    this.innerHTML = `
        <${Logout.tag} class="gap-2 grid items-center justify-center text-center mb-4"></${Logout.tag}>
        <${Nickname.tag} class="gap-2 grid items-center justify-center text-center mb-4"></${Nickname.tag}>
        <div
          room-selection
          class="grid justify-center items-center gap-2"
        >
          <label class="text-center" ${ATTRIBUTE_TRANSLATION_KEY}="room"></label>
          <input room_input class="text-center" type="text" value="${lastRoom}">
          <button join_button ${ATTRIBUTE_TRANSLATION_KEY}="join" class="${GLOBALS.buttonClasses} bg-red-900 mt-4"></button>
        </div> 
      `;
    (await TranslationController.instance).init();
    await animateCSS(this, "zoomIn");
    const joinRoom = async () => {
      const roomName = this.querySelector<HTMLInputElement>(
        "[room-selection] input",
      )!.value!;
      if (!roomName) {
        const translationController = await TranslationController.instance;
        const language = await translationController.language;
        Notify.warning(TRANSLATIONS.enterRoomName.values.text[language]);
      }
      await store.setItem("lastroom", roomName);
      const urlParams = new URLSearchParams(globalThis.location.search);
      urlParams.set("room", roomName);
      const newURL = `${globalThis.location.pathname}?${urlParams.toString()}`;
      globalThis.location.href = newURL;
    };
    this.querySelector<HTMLButtonElement>(`[join_button]`)?.addEventListener(
      "click",
      joinRoom,
    );
    this.querySelector<HTMLInputElement>("[room_input]")?.addEventListener(
      "keydown",
      (e) => {
        if (e.key === "Enter") {
          joinRoom();
        }
      },
    );
  }

  private async showRoom(roomName: string) {
    App.getInstance<App>().then((controller) => controller.showRoom(roomName));
  }
}
safeCustomDefine(RoomSelection);
