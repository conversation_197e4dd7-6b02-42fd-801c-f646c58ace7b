import { GenericComponent } from "../core/GenericComponent.ts";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "GL<PERSON><PERSON>LS";
import { ATTRIBUTE_TRANSLATION_KEY, TRANSLATIONS } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { TranslationController } from "controllers/TranslationController.ts";
import { Notify } from "notiflix";

export class ShareRoom extends GenericComponent {
  connectedCallback() {
    this.innerHTML = `
        <button 
            class="${GLOBALS.buttonClasses} bg-red-900" 
            ${ATTRIBUTE_TRANSLATION_KEY}="invite"
        ></button>
        `;
    this.querySelector("button")?.addEventListener("click", async () => {
      if (navigator.share) {
        await navigator.share({
          title: "RPG Meet",
          url: location.href,
        });
        return;
      }
      await navigator.clipboard.writeText(location.href);
      const language = await TranslationController.instance.then(
        async (controller) => await controller.language,
      );
      Notify.success(TRANSLATIONS.linkCopied.values.text[language]);
    });
  }
}
safeCustomDefine(ShareRoom);
