import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { VIDEOCALL_SERVER_BASE_URL } from "GLOBALS";
import { Singleton } from "../core/Singleton.ts";
import { MessagesSection } from "components/sidebar/MessagesSection.ts";
import { MessageDetails, OverlayMessage } from "types/communication.ts";
import { Die } from "dice/Die.ts";
import { SwitchHideResult } from "utils/SwitchHideResult.ts";
import { Login } from "core/Login.ts";
import { Nickname } from "components/room/Nickname.ts";
import { Room } from "components/room/Room.ts";
import { TranslationController } from "controllers/TranslationController.ts";
import { TranslationLanguage } from "TRANSLATIONS";

export class Videocall extends Singleton {
  private iframe: HTMLIFrameElement = document.createElement("iframe");
  private static _nickname: string = "";

  async connectedCallback() {
    const roomName = this.getAttribute("room-name");
    if (!roomName) throw new Error('missing attribute "room-name"');
    await this.init(roomName);
  }

  private async init(roomName: string) {
    const language = await TranslationController.instance.then(
      async (controller) => await controller.language,
    );
    const iframe = this.iframe;
    iframe.allow =
      "camera; microphone; display-capture; fullscreen; clipboard-read; clipboard-write; web-share; autoplay";
    const parameters = {
      room: roomName,
      name: (await Login.email) + Date.now(),
      nickname: await Nickname.getNickname(),
      audio: "1",
      video: "1",
      avatar: "1",
      screen: "0",
      hide: "0",
      notify: "0",
      language,
    };
    const baseURL = VIDEOCALL_SERVER_BASE_URL;
    const iframeURL = new URL(`${baseURL}/join/${roomName}`);
    iframeURL.search = new URLSearchParams(parameters).toString();
    iframe.src = iframeURL.href;
    iframe.className = "w-full h-full";
    this.appendChild(iframe);

    globalThis.addEventListener("message", async (event) => {
      const content = event.data;
      if (content.type === "leaveRoom") {
        await Room.leave();
        return;
      }
      const data = content.data as MessageDetails;
      const messagesSection = document.querySelector<MessagesSection>(
        MessagesSection.tag,
      )!;
      await messagesSection.add(data);
      if (data.audio) {
        await Die.playAudioThrow(data.audio);
      }
    });

    document.addEventListener(
      "languageChanged",
      async (changeLanguageEvent) => {
        await this.sendLanguage(
          (changeLanguageEvent as CustomEvent).detail.language,
        );
      },
    );
  }

  async sendMessage(message: MessageDetails | OverlayMessage) {
    const isOverlayMessage = message.type === "overlay";
    if (isOverlayMessage) {
      const overlayMessage = message as OverlayMessage;
      this.iframe.contentWindow?.postMessage({
        type: "overlay",
        data: overlayMessage,
      }, "*");
      return;
    }

    const hidingResults: boolean = SwitchHideResult.active;
    if (hidingResults) return;
    const data: MessageDetails = {
      ...message,
      from: await Nickname.getNickname(),
    };
    this.iframe.contentWindow?.postMessage({
      type: "rpgMeet",
      data: data,
    }, "*");
  }
  async sendNickname(nickname: string) {
    this.iframe.contentWindow?.postMessage({
      type: "setNickname",
      data: nickname,
    }, "*");
  }
  async sendLanguage(language: TranslationLanguage) {
    this.iframe.contentWindow?.postMessage({
      type: "changeLanguage",
      language,
    }, "*");
  }
}

safeCustomDefine(Videocall);
