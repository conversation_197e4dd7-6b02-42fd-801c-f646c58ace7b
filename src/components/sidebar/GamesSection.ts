import { store } from "store";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { SwitchHideResult } from "utils/SwitchHideResult.ts";
import { AchtungCthulhuSet } from "../games/sets/AchtungCthulhuSet.ts";
import { AgonSet } from "../games/sets/AgonSet.ts";
import { BrokenCompassSet } from "../games/sets/BrokenCompassSet.ts";
import { CBRPNKSet } from "../games/sets/CBRPNKSet.ts";
import { CityOfMistSet } from "../games/sets/CityOfMistSet.ts";
import { CthulhuDarkSet } from "../games/sets/CthulhuDarkSet.ts";
import { DeckOfCards } from "../games/sets/DeckOfCards.ts";
import { DontRestYourHeadSet } from "../games/sets/DontRestYourHeadSet.ts";
import { FantasyWorldSet } from "../games/sets/FantasyWorldSet.ts";
import { FateSet } from "../games/sets/FateSet.ts";
import { FiascoSet } from "../games/sets/FiascoSet.ts";
import { GameRequest } from "../games/sets/GameRequest.ts";
import { GameSet } from "../games/sets/GameSet.ts";
import { PBTAGenericSet } from "../games/sets/PBTAGenericSet.ts";
import { LFRSet } from "../games/sets/LFRSet.ts";
import { NotTheEndSet } from "../games/sets/NotTheEndSet.ts";
import { OrdemParanormalSet } from "../games/sets/OrdemParanormalSet.ts";
import { PolyhedricDiceSet } from "../games/sets/PolyhedricDiceSet.ts";
import { SeventhSeaSet } from "../games/sets/SeventhSeaSet.ts";
import { SixBulletsSet } from "../games/sets/SixBulletsSet.ts";
import { SpireSet } from "../games/sets/SpireSet.ts";
import { ValravenSet } from "../games/sets/ValravenSet.ts";
import { VampireSet } from "../games/sets/VampireSet.ts";
import { VampireV20Set } from "../games/sets/VampireV20Set.ts";
import { Section } from "./Section.ts";
import { SavageWorldsSet } from "sets/SavageWorldsSet.ts";
import { AvatarSet } from "../games/sets/AvatarSet.ts";
import { GURPSSet } from "../games/sets/GURPSSet.ts";

const games: typeof GameSet[] = [
  PolyhedricDiceSet,
  DeckOfCards,
  SeventhSeaSet,
  AchtungCthulhuSet,
  AgonSet,
  AvatarSet,
  BrokenCompassSet,
  CBRPNKSet,
  CityOfMistSet,
  CthulhuDarkSet,
  DontRestYourHeadSet,
  FantasyWorldSet,
  FateSet,
  FiascoSet,
  GURPSSet,
  LFRSet,
  NotTheEndSet,
  OrdemParanormalSet,
  PBTAGenericSet,
  SavageWorldsSet,
  SpireSet,
  SixBulletsSet,
  ValravenSet,
  VampireSet,
  VampireV20Set,
  GameRequest,
];

export class GamesSection extends Section {
  static override get label(): string {
    return "Game";
  }
  override async connectedCallback() {
    await super.connectedCallback();
    const gameTags = games.map((game) => game.tag);
    const localforageId = `${
      (this.constructor as typeof GamesSection)["tag"]
    }-last-option-selected`;
    const lastOptionSelected = await store.getItem(localforageId);
    const optionsHTML = games.map((game) => {
      const selected = lastOptionSelected === game.tag ? "selected" : "";
      const translation = game.translatable
        ? `${ATTRIBUTE_TRANSLATION_KEY}="${game.label}"`
        : "";
      return `<option
                        value="${game.tag}"
                        class="cursor-pointer ${
        game.optionAdditionalStyles.join(" ")
      }"
                        ${translation}
                        ${selected}
                    >${game.translatable ? "" : game.label}</option>`;
    }).join("");
    const selectHTML = `
            <div class="flex flex-wrap">
                <select id="game-list" class="w-full cursor-pointer uppercase border text-black" game_selection>
                    ${optionsHTML}
                </select>
            </div>
        `;
    const gamesHTML = games.map((game) => {
      return `<${game.tag} class="hidden"></${game.tag}>`;
    }).join("");
    this.content = `
            ${selectHTML}
            <div class="mt-2" game_container>
                ${gamesHTML}
            </div>
            <div class="col-span-4 mt-3">
                    <${SwitchHideResult.tag}></${SwitchHideResult.tag}>
            </div>
        `;
    const showGame = (gameTag: string) => {
      if (!gameTags.includes(gameTag)) {
        return;
      }
      this.querySelectorAll(`[game_container] > *`).forEach((game) => {
        game.classList.add("hidden");
      });
      this.querySelector(`[game_container] > ${gameTag}`)?.classList.remove(
        "hidden",
      );
    };
    const select = this.querySelector("[game_selection]") as HTMLSelectElement;
    select.addEventListener("change", async () => {
      showGame(select.value);
      await store.setItem(localforageId, select.value);
    });
    showGame(select.value);
  }
}
safeCustomDefine(GamesSection);
