import { store } from "store";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "G<PERSON><PERSON><PERSON><PERSON>";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { MessageDetails } from "types/communication.ts";
import { ResultComponents } from "types/results.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Section } from "./Section.ts";
import { Room } from "../room/Room.ts";

export class MessagesSection extends Section {
  debug: boolean = false;
  static override get label(): string {
    return "Messages";
  }
  static formatDiceRoll(resultComponents: ResultComponents): string {
    const picsHTML = resultComponents.pics
      .map((svg: string) => {
        return `<span class="w-14">${svg}</span>`;
      }).join(" ");
    const resultText = `
            <b>${resultComponents.description}</b>
            <span class="flex flex-wrap">${picsHTML}</span>
        `;
    return resultText;
  }
  override async connectedCallback() {
    await super.connectedCallback();
    this.querySelector("details")!.setAttribute("open", "true");

    this.debug = await store.getItem(
      GLOBALS.PREFIX + "-debug",
    ) as boolean;
    this.content = `
            <div 
                container_messages 
                class="overflow-scroll h-64 overflow-y-auto overflow-x-hidden" 
                style="scroll-behavior:smooth;scroll-snap-type: y mandatory"
            ></div>
        `;
    const roomId: string = await Room.getId();
    if (await store.getItem("lastroom") === roomId) {
      const lastSessionMessages = await store.getItem("messages") as string;
      const messagesContainer = this.querySelector<HTMLDivElement>(
        "[container_messages]",
      )!;
      messagesContainer.innerHTML = lastSessionMessages;
    } else {
      await store.removeItem("messages");
    }
  }
  async add(messageDetails: MessageDetails) {
    this.flash();
    const animations: { [key: string]: string } = {
      "die": "animate__bounceInDown",
      "token": "animate__zoomInUp",
    };
    const animationString: string =
      "animate__animated " + animations[messageDetails.type] || "";
    const now: Date = new Date(messageDetails.timestamp || Date.now());
    const stringTimestamp: string = now.getHours() + ":" +
      now.getMinutes().toString().padStart(2, "0");
    if (messageDetails.debug && !this.debug) {
      return;
    }
    const freshClasses: string = "fresh-message border-b border-green-200";
    const newMessage: HTMLDivElement = document.createElement("div");
    const message: string = messageDetails.text || messageDetails.type;
    const nickname = messageDetails.from || messageDetails.nickname;
    const nicknameFormatted = nickname !== "You"
      ? nickname
      : `<span ${ATTRIBUTE_TRANSLATION_KEY}="You"></span>`;
    newMessage.innerHTML =
      `<p class="m-1 ${animationString} ${freshClasses}">[${stringTimestamp} - ${nicknameFormatted}] ${message}</p>`;
    // add drop shadow to all svgs
    newMessage.querySelectorAll("svg").forEach((svg: SVGElement): void => {
      svg.classList.add("drop-shadow-lg", "filter");
    });
    const messagesContainer: HTMLDivElement = this.querySelector(
      "[container_messages]",
    ) as HTMLDivElement;
    messagesContainer?.prepend(newMessage);
    this.querySelectorAll(".fresh-message").forEach((element) => {
      const timeout = setTimeout(() => {
        element.classList.remove(...freshClasses.split(" "));
        clearTimeout(timeout);
      }, 5000);
    });
    messagesContainer?.scroll({
      top: -messagesContainer.scrollHeight,
      left: 0,
      behavior: "smooth",
    });
    if (this.getBoundingClientRect().top > (globalThis.innerHeight - 150)) {
      this.scrollIntoView();
    }
    const htmlWithoutAnimations: string = Array.from(
      this.querySelectorAll("[container_messages] div"),
    )
      .slice(0, 99).map((el) => el.innerHTML).join("")
      .replace(
        new RegExp(
          "\\b(" + Object.values(animations).join("|") +
            freshClasses.replace(/\s/g, "|") + ")\\b",
          "gi",
        ),
        " ",
      );
    await store.setItem(
      "messages",
      htmlWithoutAnimations,
    );
    return newMessage.innerHTML;
  }
  flash() {
    const details = this.querySelector("details");
    if (!details || details?.open) return;
    const flashElement = details.querySelector("summary")!;
    const animationClasses = [
      "animate__animated",
      "animate__flash",
      "bg-green-200",
      "text-black",
    ];
    flashElement.classList.add(...animationClasses);
    flashElement.addEventListener("animationend", (): void => {
      flashElement.classList.remove(...animationClasses);
    });
  }
}
safeCustomDefine(MessagesSection);
