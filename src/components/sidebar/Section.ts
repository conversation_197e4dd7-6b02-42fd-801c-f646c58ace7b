import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { showHideSection } from "utils/showHideSection.ts";
import { GenericComponent } from "../core/GenericComponent.ts";
import { store } from "store";

export class Section extends GenericComponent {
  static get label(): string {
    return "Section";
  }
  get labelExtended(): string {
    return "";
  }
  set content(htmlContent: string) {
    const content = this.querySelector(`[section-content]`);
    if (!content) {
      throw new Error(
        `No content found for ${(this.constructor as typeof Section)["tag"]}`,
      );
    }
    content.innerHTML = htmlContent;
  }
  async connectedCallback() {
    const localforageId: string = `${this.tagName.toLowerCase()}-visibility`;
    const lastStatus = await store.getItem<string>(localforageId);
    this.innerHTML = `
            <details class="w-full" ${
      lastStatus || lastStatus === null ? "open" : ""
    }>
                <summary class="flex gap-1 px-2 align-text-bottom cursor-pointer text-sm uppercase">
                    <span ${ATTRIBUTE_TRANSLATION_KEY}="${
      (this.constructor as typeof Section)["label"]
    }"></span>
                </summary>
                <div section-content class="p-2">
                </div>
            </details>
        `;
    const details = this.querySelector<HTMLDetailsElement>("details");
    details?.addEventListener("toggle", () => {
      if (details.open) {
        this.scrollIntoView(false);
      }
      store.setItem(localforageId, details.open);
    });
    this.querySelector("[button-hide]")?.addEventListener("click", async () => {
      await showHideSection((this.constructor as typeof Section)["tag"], false);
    });

    // const summary: Element = this.querySelector('summary');
    // summary.addEventListener('mouseover', () => { summary.innerHTML = this.labelExtended || this.constructor['label'] });
    // summary.addEventListener('mouseout', () => { summary.innerHTML = this.constructor['label'] });
  }
}
