import { Singleton } from "../core/Singleton.ts";
import { Sidebar } from "./Sidebar.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";

export class ShowSidebarButton extends Singleton {
  static override class = [
    "md:hidden",
    "absolute",
    "top-0",
    "left-0",
    "text-3xl",
    "ml-2",
    "z-40",
    "cursor-pointer",
  ];
  async connectedCallback() {
    this.innerHTML = ` 
        <iconify-icon
          icon="mdi:menu"
          class="align-middle"  
          aria-label="show sidebar"
        ></iconify-icon>
        `;
    this.addEventListener("click", () => {
      Sidebar.getInstance<Sidebar>().then(async (sidebar) =>
        await sidebar.show()
      );
    });
  }
}
safeCustomDefine(ShowSidebarButton);
