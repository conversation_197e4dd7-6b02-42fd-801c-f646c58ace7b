import { GL<PERSON><PERSON><PERSON>, prefixedId } from "GL<PERSON><PERSON><PERSON>";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { NavbarSectionsContainer } from "../navbar/NavbarSectionsContainer.ts";
import { Credits } from "./credits.ts";
import { MessagesSection } from "./MessagesSection.ts";
import { Logo } from "../core/Logo.ts";
import { GamesSection } from "components/sidebar/GamesSection.ts";
import { NavBar } from "components/navbar/Navbar.ts";
import { Singleton } from "../core/Singleton.ts";
import { animateCSS } from "utils/animateCss.ts";

export class Sidebar extends Singleton {
  static override class = [
    "md:grid",
    "text-base",
    "grid-rows-12",
    "animate__faster",
    "hidden",
  ];
  connectedCallback() {
    const versionName = "1";
    this.innerHTML = `
            <div main class="row-span-10 z-50"> 
                <${NavBar.tag}></${NavBar.tag}>
                <${NavbarSectionsContainer.tag}></${NavbarSectionsContainer.tag}> 
                <${GamesSection.tag}></${GamesSection.tag}>
                <${MessagesSection.tag}></${MessagesSection.tag}> 
            </div>
            <div institutional class="row-span-2">
                <div class="text-center mt-1">
                    <div class="grid grid-flow-col items-center">
                        <span class="grid justify-end oswald">RPG Meet</span>
                        <${Logo.tag} class="inline-block w-20 cursor-pointer" ${
      prefixedId("animation")
    }-animation="true"></${Logo.tag}>
                    </div>
                    <div class="text-xs my-2" title="${versionName}" >
                        <span ${ATTRIBUTE_TRANSLATION_KEY}="madeBy"></span>
                        <a href="https://github.com/marcomow" target="_blank">marko</a>
                    </div>
                </div>
                <div class="flex justify-center gap-4 text-xs uppercase mb-2">
                    <span switch-credits class="cursor-pointer">CREDITS</span>
                    <span changelog-link href="https://rpgmeet.app/changelog" class="cursor-pointer">CHANGELOG</span>
                </div>
                <div class="grid grid-cols-3 gap-x-3 px-3">
                    <a href="https://www.comebackalive.in.ua/donate" target="_blank" class="${GLOBALS.buttonClasses}" ${ATTRIBUTE_TRANSLATION_KEY}="Donate" style="background-image:url('public/assets/img/ua.svg')"></a>
                    <a href="https://t.me/rpgmeetextension" target="_blank" class="${GLOBALS.buttonClasses} bg-blue-900 hover:bg-blue-800">Telegram</a>
                    <a href="mailto:<EMAIL>" target="_blank" class="${GLOBALS.buttonClasses} bg-green-900 hover:bg-green-800 ">Feedback</a>
                </div> 
                <${Credits.tag} class="hidden"></${Credits.tag}>
            </div>
    `;
    const switchCredits = this.querySelector(`[switch-credits]`)!;
    const credits = this.querySelector(`${Credits.tag}`)!;
    switchCredits.addEventListener("click", () => {
      credits.classList.toggle("hidden");
      credits.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    });
    const changelogLink = this.querySelector(`[changelog-link]`)!;
    changelogLink.addEventListener("click", () => {
      globalThis.open(changelogLink.getAttribute("href")!, "_blank");
    });
  }
  async hide() {
    await animateCSS(this, "slideOutLeft", "faster");
    this.classList.add("hidden");
  }
  async show() {
    this.classList.remove("hidden");
    await animateCSS(this, "slideInLeft", "faster");
  }
}
safeCustomDefine(Sidebar);
