import settings from "../../settings/credits.json" with { type: "json" };
import { ObjectCredit } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { GenericComponent } from "../core/GenericComponent.ts";

export class Credits extends GenericComponent {
  async connectedCallback() {
    const credits: ObjectCredit[] = settings;
    this.innerHTML = credits
      .map((credit: ObjectCredit) => {
        return `<div class="p-2 hover:scale-110">
                <a href="${credit.url}" target="_blank">${credit.name}</a>
            </div>`;
      })
      .join("") +
      `
                <fieldset class="border p-2">
                    <legend>Icons from <a href="https://thenounproject.com" target="_blank">the Noun Project</a></legend>
                    <p>Compass by Chinnaking</p>
                    <p>Skull by <PERSON></p>
                    <p>Compass by <PERSON><PERSON><PERSON><PERSON></p>
                    <p>Owl by <PERSON></p>
                    <p>Flash by TTHNga</p>
                    <p>Mask by <PERSON><PERSON></p>
                    <p>Raven by Imogen Oh</p>
                    <p>Sword by <PERSON>olsi</p>
                    <p>Joker by <PERSON>ik</p>         
                    <p>Spade, Diamond, Clubs, Heart by Haik Dettmann</p>
                    <p>King by verry poernomo</p>
                    <p>Skull by ntgCleaner</p>
                </fieldset>
            `;
  }
}
safeCustomDefine(Credits);
