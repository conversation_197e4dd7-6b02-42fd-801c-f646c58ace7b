import { OverlaySettings } from "types/OverlaySettings.ts";
import { WEBCAM_OVERLAY_LOCALFORAGE_ID } from "GL<PERSON><PERSON><PERSON>";
import { store } from "store";

let overlayController: OverlayController;

export class OverlayController {
  static get instance(): Promise<OverlayController> {
    return Promise.resolve(
      overlayController || (overlayController = new OverlayController()),
    );
  }
  get ownOverlaySettings(): Promise<OverlaySettings> {
    const overlaySettings: Promise<OverlaySettings> = store.getItem(
      WEBCAM_OVERLAY_LOCALFORAGE_ID,
    ).then((settings) => {
      if (!settings) {
        return {
          active: false,
          title: "",
          titleFontSize: 1,
          subtitle: "",
          subtitleFontSize: 1,
          avatar: "",
          tableFontSize: 1,
        };
      }
      return settings as OverlaySettings;
    });
    return overlaySettings;
  }
}
