import {
  ATTRIBUTE_TRANSLATION_KEY,
  ATTRIBUTE_TRANSLATION_VALUE,
  TranslationKey,
  TranslationLanguage,
  TRANSLATIONS,
} from "TRANSLATIONS";
import { devLog } from "utils/devLog.ts";
import { store } from "store";

let translationController: TranslationController;
const notFoundTranslations = new Set<TranslationKey>();
export class TranslationController {
  static get instance(): Promise<TranslationController> {
    return Promise.resolve(
      translationController ||
        (translationController = new TranslationController()),
    );
  }
  get language(): Promise<TranslationLanguage> {
    return (store.getItem(`language`) ||
      Promise.resolve("en")) as Promise<TranslationLanguage>;
  }
  set language(language: Promise<TranslationLanguage>) {
    store.setItem(`language`, language);
  }
  async init() {
    const language = await this.language;
    const detectedLanguage = navigator.language.split("-")[0];
    await this.setLanguage(language || detectedLanguage || "en");
  }
  async setLanguage(language: TranslationLanguage) {
    this.language = Promise.resolve(language);
    const elements = Array.from(
      document.querySelectorAll<HTMLElement>(`[${ATTRIBUTE_TRANSLATION_KEY}]`),
    );
    for (const element of elements) {
      const translationController = await TranslationController.instance;
      await translationController.translateElementContent(element, language);
      if (element.tagName === "OPTION") {
        const option = element as HTMLOptionElement;
        option.innerHTML = option.getAttribute(ATTRIBUTE_TRANSLATION_VALUE) ||
          option.getAttribute(ATTRIBUTE_TRANSLATION_KEY) || "";
      }
    }
    document.dispatchEvent(
      new CustomEvent("languageChanged", {
        detail: {
          language,
        },
        bubbles: true,
      }),
    );
  }
  async getTranslation(
    attributeKey: TranslationKey,
    selectedLanguage?: string,
  ): Promise<string> {
    const translation = TRANSLATIONS[attributeKey];
    if (!translation) {
      devLog(`Translation for key "${attributeKey}" not found.`, {
        action: "warn",
      });
      return "";
    }
    const language = selectedLanguage || await this.language;
    const value: string = translation.base.replace(
      /\{(.*?)\}/g,
      (_, key: string) => {
        return translation
          .values[key as keyof typeof translation.values][language];
      },
    );
    return value;
  }
  async translateElementContent(
    element: HTMLElement,
    selectedLanguage?: string,
  ) {
    const attributeKey = element.getAttribute(
      ATTRIBUTE_TRANSLATION_KEY,
    )! as TranslationKey;
    if (!attributeKey) {
      if (!!attributeKey && !notFoundTranslations.has(attributeKey)) {
        devLog(element, { location: "console" });
        notFoundTranslations.add(attributeKey);
        devLog(
          `Translation key "${attributeKey}" not found in element ${
            "tag" in element ? element["tag"] : element.tagName
          }`,
          { location: "console" },
        );
      }
      return;
    }
    const value = await this.getTranslation(attributeKey, selectedLanguage);
    element.setAttribute(ATTRIBUTE_TRANSLATION_VALUE, value);
  }
}
