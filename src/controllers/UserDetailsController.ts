import { Nickname } from "components/room/Nickname.ts";

let userDetailsController: UserDetailsController;
export class UserDetailsController {
  static get instance(): UserDetailsController {
    return userDetailsController ||
      (userDetailsController = new UserDetailsController());
  }
  static get nickname(): Promise<string> {
    return Nickname.getNickname();
  }
  get ownParticipantId() {
    return "local";
  }
}
