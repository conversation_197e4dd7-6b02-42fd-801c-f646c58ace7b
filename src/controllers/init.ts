import { App } from "../components/core/App.ts";
import { TranslationController } from "controllers/TranslationController.ts";
import { devLog } from "utils/devLog.ts";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";

const initialisers: { name: string; func: () => Promise<void> }[] = [
  {
    name: "app",
    func: async () => {
      const app = new App();
      document.body.appendChild(app);
    },
  },
  {
    name: "translation controller",
    func: async () => {
      const translationController = await TranslationController.instance;
      await translationController.init();
      const translate = async (element: HTMLElement) =>
        await translationController.translateElementContent(element);
      const observer = new MutationObserver(async (mutations) => {
        for (const mutation of mutations) {
          if (
            mutation.type === "attributes" &&
            mutation.attributeName !== ATTRIBUTE_TRANSLATION_KEY
          ) return;
          const element = mutation.target as HTMLElement;
          if (element.hasAttribute(ATTRIBUTE_TRANSLATION_KEY)) {
            await translate(element);
          }
          element.querySelectorAll<HTMLElement>(
            `[${ATTRIBUTE_TRANSLATION_KEY}]`,
          ).forEach(translate);
        }
      });
      const app = await App.getInstance<App>();
      observer.observe(app, {
        subtree: true,
        childList: true,
        attributes: true,
        attributeFilter: [ATTRIBUTE_TRANSLATION_KEY],
      });
    },
  },
  {
    name: "refresh",
    func: async () => {
      refresh();
    },
  },
];

const initialise = async () => {
  for (const initialiser of initialisers) {
    devLog(`initialising ${initialiser.name}`);
    await initialiser.func();
    devLog(`initialised ${initialiser.name}`);
  }
};

document.addEventListener("DOMContentLoaded", async () => {
  await initialise();
});

const refresh = () => {
  const currentURL = new URL(globalThis.location.href);
  const isLocal = currentURL.hostname === "localhost" ||
    currentURL.hostname === "127.0.0.1";
  if (!isLocal) {
    return;
  }
  let websocket: WebSocket, interval: number;

  function log(message: string) {
    console.info("[refresh] ", message);
  }

  function reload() {
    location.reload();
  }
  const url = `${location.origin.replace("http", "ws")}/_r`;

  function setup(
    func?: (event: Event) => void,
  ) {
    websocket ??= new WebSocket(url);
    websocket.addEventListener("open", (event) => {
      func?.(event);
    });
    websocket.addEventListener("message", () => {
      log("reloading...");
      reload();
    });
    websocket.addEventListener("close", () => {
      log("connection lost - reconnecting...");
      clearTimeout(interval);
      interval = setTimeout(() => setup(reload), 1000);
    });
  }
  setup(console.log);
};
