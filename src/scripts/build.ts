import { build as esbuild } from "npm:esbuild@0.25.0";
import { denoPlugins } from "jsr:@luca/esbuild-deno-loader@^0.11.1";
import { getCurrentBranchName } from "../utils/gitUtils.ts";

export const build = async () => {
  console.log(`Building...`);
  const template = await Deno.readTextFile(
    new URL("../templates/base.html", import.meta.url),
  );
  const bundler = await esbuild({
    plugins: [...denoPlugins()],
    entryPoints: [
      new URL("../controllers/init.ts", import.meta.url).pathname,
    ],
    bundle: true,
    write: true,
    outdir: new URL("../../public/js", import.meta.url).pathname,
    splitting: true,
    format: "esm",
    chunkNames: "chunks/[name]-[hash]",
    platform: "browser",
    keepNames: true,
    target: "es2020",
    treeShaking: true,
  });
  const branchName = await getCurrentBranchName();
  const titleSuffix = branchName !== "production" ? ` - ${branchName}` : "";
  const content = template
    .replace(
      `</head>`,
      `</head>`,
    )
    .replace(
      `<title>RPG Meet</title>`,
      `<title>RPG Meet${titleSuffix}</title>`,
    );
  await Deno.writeTextFile(
    new URL("../../public/index.html", import.meta.url),
    content,
  );
  console.log(`Built!`);
};

if (Deno.args[0] === "--build") {
  await build();
  Deno.exit();
}
