import { Octokit } from "@octokit/rest";
export const createGithubIssue = async (options: {
  name: string;
  body: string;
}) => {
  const { name, body } = options;
  const octokit = new Octokit({
    auth: Deno.env.get("GITHUB_TOKEN"),
    userAgent: "rpg-meet-api",
  });
  const issueDetails = {
    repo: Deno.env.get("GITHUB_REPO")!,
    owner: Deno.env.get("GITHUB_OWNER")!,
    title: name,
    body,
    labels: ["enhancement", "new-game"],
  };
  const issue = await octokit.rest.issues.create(issueDetails);
  return issue;
};
