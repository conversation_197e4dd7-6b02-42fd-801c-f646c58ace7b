import { serveDir, serveFile } from "@std/http";
import { apiRouter } from "api/router.ts";
import { renderMarkdown } from "utils/markdownRenderer.ts";

let server: Deno.HttpServer;
let reloadSocket: WebSocket;

export const initialiseServer = async () => {
  console.log("initialising server");
  await server?.shutdown();
  server = Deno.serve({
    hostname: "localhost",
    port: 8080,
  }, async (request) => {
    if (request.headers.get("upgrade") === "websocket") {
      return setupWebsocketForRefresh(request);
    }
    const path = new URL(request.url).pathname;
    if (path.endsWith(".md")) {
      // Extract language parameter from URL if present
      const url = new URL(request.url);
      const language = url.searchParams.get("lang") || undefined;

      const fileContent = await Deno.readTextFile(
        new URL(`../../${path}`, import.meta.url).pathname,
      );
      return await renderMarkdown(fileContent, language);
    }
    if (path.startsWith("/public/")) {
      const fsRoot = new URL("../../public/", import.meta.url).pathname;
      const response = await serveDir(request, {
        fsRoot,
        urlRoot: "public",
      });
      response.headers.set("Cache-Control", "no-store");
      return response;
    }
    if (path.startsWith("/api")) {
      return await apiRouter(request);
    }

    // Serve landing page
    if (path === "/landing.html") {
      return serveFile(
        request,
        new URL("../../public/landing.html", import.meta.url).pathname,
      );
    }
    const fileResponse = await serveFile(
      request,
      new URL("../../public/index.html", import.meta.url).pathname,
    );

    // Apply no-cache headers to the response
    const headers = new Headers(fileResponse.headers);
    headers.set("Cache-Control", "no-store");

    return new Response(fileResponse.body, {
      status: fileResponse.status,
      statusText: fileResponse.statusText,
      headers,
    });
  });
  console.log("server started");
  console.log("send reloading message...");
  reloadSocket?.send("reload");
  console.log("sent reloading message");
};

const setupWebsocketForRefresh = (request: Request) => {
  const { socket, response } = Deno.upgradeWebSocket(request);
  socket.onopen = () => {
    console.log("websocket connected");
  };
  socket.onmessage = (event) => {
    console.log(`RECEIVED: ${event.data}`);
  };
  socket.onclose = () => console.log("websocket closed");
  socket.onerror = (error) => console.error("ERROR:", error);
  reloadSocket = socket;
  return response;
};
