import { sendTelegramNotification } from "./send-telegram-notification.ts";
import "@std/dotenv/load";

const currentBranch = new Deno.Command("git", {
  args: ["rev-parse", "--abbrev-ref", "HEAD"],
});

const { stdout } = await currentBranch.output();
const branchName = new TextDecoder().decode(stdout).trim();

await sendTelegramNotification({
  message: `Deployment to ${branchName} completed https://${branchName === "production" ? "" : `${branchName}.`}rpgmeet.app`,
});
