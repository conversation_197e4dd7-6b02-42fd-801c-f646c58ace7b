import { Telegram } from "@gramio/wrappergram";

export const sendTelegramNotification = async (options: {
  message: string;
}) => {
  const { message } = options;
  const bot = new Telegram(Deno.env.get("TELEGRAM_TOKEN")! as string);
  const chatId = Deno.env.get("TELEGRAM_CHAT_ID")!;
  const sendMessageOptions = {
    chat_id: chatId,
    text: message,
    parse_mode: "HTML",
  } as const;
  console.log({ sendMessageOptions });
  const sentMessage = await bot.api.sendMessage(sendMessageOptions);
  console.log({ sentMessage });
  return sentMessage;
};
