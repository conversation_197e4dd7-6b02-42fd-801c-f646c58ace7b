<!DOCTYPE html>
<html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#252121" />
    <title>RPG Meet - {{title}}</title>
    <link rel="stylesheet" href="/public/fonts.css" />
    <link rel="stylesheet" href="/public/markdown.css" />
  </head>
  <body>
    <nav class="md-navbar">
      <a href="/" class="md-navbar-logo">
        <img src="/public/favicon.svg" alt="RPG Meet Logo" />
        RPG Meet
      </a>
      <div class="md-navbar-actions">
        <div class="translate-button" id="google_translate_element"></div>
        <a href="/" class="md-navbar-back">Return to App</a>
      </div>
    </nav>
    <div class="markdown-container">
      {{content}}
    </div>

    <!-- Google Translate Script -->
    <script type="text/javascript">
      function googleTranslateElementInit() {
        // Auto-translate if the page language is not the same as the content language
        if (
          "{{language}}" !== "en" &&
          navigator.language !== "{{language}}"
        ) {
          // Trigger translation to the specified language
          document.cookie = "googtrans=/en/{{language}}";
        }
        new google.translate.TranslateElement(
          {
            pageLanguage: "en",
            layout:
              google.translate.TranslateElement.InlineLayout.SIMPLE,
            autoDisplay: false,
            includedLanguages: "", // all languages
            gaTrack: false,
            gaId: "",
          },
          "google_translate_element",
        );
        // Hide the translation banner but keep the dropdown
        const bannerInterval = setInterval(function () {
          // Hide the banner frame
          const bannerFrame = document.querySelector(
            ".goog-te-banner-frame",
          );
          if (bannerFrame) {
            bannerFrame.style.display = "none";
          }

          // Reset body position
          if (document.body.style.top) {
            document.body.style.top = "0px";
          }

          // Only clear interval when both conditions are met
          if (bannerFrame && document.body.style.top) {
            clearInterval(bannerInterval);
          }
        }, 100);
      }
    </script>
    <script
      type="text/javascript"
      src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"
    ></script>
  </body>
</html>
