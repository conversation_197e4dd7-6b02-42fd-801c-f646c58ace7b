type filter =
  | "blur"
  | "brightness"
  | "contrast"
  | "grayscale"
  | "invert"
  | "saturate"
  | "sepia";
type filterKey = `filter-${filter}`;
export type OverlaySettings =
  & {
    active: boolean;
    mirror?: boolean;
    title: string;
    titleFontSize: number;
    subtitle: string;
    subtitleFontSize: number;
    avatar: string;
    tableFontSize: number;
  }
  & {
    [key in filterKey]?: number;
  }
  & {
    rows?: number;
    columns?: number;
  }
  & {
    [key in `table_${number}_${number}`]?: string;
  };
export type OverlaySettingSectionConfig = {
  title: string;
  settings: OverlaySettingConfig[];
};
export type OverlaySettingConfig = {
  inputType: "text" | "file" | "number" | "checkbox" | "button";
  name: keyof OverlaySettings | "remove";
  colSpan: number;
  label?: string;
  labelColumns?: number;
  translatable?: boolean;
  min?: number;
  max?: number;
  step?: number;
  "default-value"?: string | number | boolean;
};
