import { PlayAudioOptions } from "types/results.ts";
import { OverlaySettings } from "types/OverlaySettings.ts";

export type MessageDetails = {
  type:
    | "die"
    | "token"
    | "text"
    | "diceroll"
    | "debug";
  text?: string;
  from?: string;
  timestamp?: number;
  debug?: boolean;
  nickname?: string;
  hidingResults?: boolean;
  audio?: PlayAudioOptions;
};

export type OverlayMessage = {
  type: "overlay";
  nickname: string;
  overlaySettings: OverlaySettings;
};
