import { TranslationKey } from "TRANSLATIONS";

export type ENV = "development" | "production" | "unknown";

export type ObjectCredit = {
  name: string;
  url: string;
};
export interface DieElementSettings {
  type: "die" | "group" | "group-button";
}
export interface DieSettings extends DieElementSettings {
  type: "die";
  tag: string;
  color?: string;
  size: number;
  name: string;
  singleThrowDisabled?: boolean;
  single?: true;
  min?: number;
  max?: number;
  step?: number;
  hasSum?: boolean;
  groupRoll?: {
    id: string;
    optional?: boolean;
  };
  span?: number;
  buttonText?: {
    text: TranslationKey | string;
    style?: FontStyle;
  };
  mod?: string;
  label?: {
    text: TranslationKey;
    position?: "left" | "right" | "top" | "bottom";
  };
  flavor?: string;
}
export interface DiceGroupSettings extends DieElementSettings {
  type: "group";
  legend: string;
  elements: (DieSettings | DiceGroupRollButtonSettings)[];
  columNumber?: number;
  span?: number;
  collapsible?: boolean;
  collapsed?: boolean;
  additionalClasses?: string[];
}
export interface DiceGroupRollButtonSettings extends DieElementSettings {
  type: "group-button";
  id: string;
  buttonText?: string;
  span?: number;
  additionalClasses?: string[];
  resultConsolidation: {
    strategy: "individual";
  } | {
    strategy: "name" | "size";
    sum?: boolean;
  };
  resultSort?: "ascending" | "descending" | "none";
  flavor?: string;
}
export type ContainerDiceIcon = {
  [key in (number | string)]: string | string[];
};
export type FontStyleKey =
  | "font-family"
  | "font-size"
  | "font-weight"
  | "fill"
  | "stroke"
  | "stroke-width";
export type FontStyle = Record<FontStyleKey, string>;
export interface Tile {
  imgPath: string;
  cssSize: string;
  fileName: string;
}
export interface GeneratedPicTile extends Tile {
  templateName: string;
}
