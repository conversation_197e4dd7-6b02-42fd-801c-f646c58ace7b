import { DieClassName } from "components/games/playables/dice/helpers/diceClassesIndex.ts";
// import { MessageDetails } from "./communication.ts";

export type ResultValue = string;
export type ResultDescription = string;
export type ResultPic = string;
export type RollResult = ResultValue | ResultPic;
export interface ResultComponents {
  description: ResultDescription;
  values: ResultValue[];
  pics: ResultPic[];
}
export interface Result extends ResultComponents {
  dieName: string;
  dieDisplayName: string;
}
export interface DisplayResultInput {
  pics: ResultPic[];
  nickname?: string;
  overlayAnimations?: string[];
}
export interface TextResultInput {
  values: ResultValue[];
  dieName: string;
  dieDisplayName: string;
  customDieSize?: string | number;
}
export interface DiceRollOptions {
  die: DieClassName | "group";
  dieName: string;
  result: {
    values: ResultValue[];
    pics: ResultPic[];
    description: ResultDescription;
  };
  hidingResults?: boolean;
}
export interface PlayAudioOptions {
  values: ResultValue[];
  throwAudios: string[];
  resultAudios: string[];
}
export interface ResultInput {
  values: ResultValue[];
  dieName?: string;
  dieDisplayName?: string;
}
export interface DiceRoll {
  die: DieClassName;
  dieName: string;
  result: ResultValue;
  hidingResults?: boolean;
}
// export interface DiceRollMessage extends MessageDetails {
//   type: "diceroll";
//   die: DieClassName | "group";
//   timestamp: number;
//   nickname: string;
//   result: {
//     description: ResultDescription;
//     pics: ResultPic[];
//     values: ResultValue[];
//   };
//   dieName?: string;
// }
