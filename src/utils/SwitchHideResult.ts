import { store } from "store";
import { ATTRIBUTE_TRANSLATION_KEY } from "TRANSLATIONS";
import { safeCustomDefine } from "./safeCustomDefine.ts";
import { GenericComponent } from "../components/core/GenericComponent.ts";

export class SwitchHideResult extends GenericComponent {
  static get idLocalForage(): string {
    return `${SwitchHideResult.tag}-active`;
  }
  static get active(): boolean {
    const switchHide = document.querySelector<SwitchHideResult>(
      SwitchHideResult.tag,
    );
    if (!switchHide) throw new Error("SwitchHideResult not found");
    return switchHide.active;
  }
  get active(): boolean {
    const checkbox = this.querySelector<HTMLInputElement>(`[type="checkbox"]`)!;
    return checkbox.checked;
  }
  set active(value: boolean) {
    const checkbox = this.querySelector<HTMLInputElement>(`[type="checkbox"]`)!;
    checkbox.checked = value;
  }
  async connectedCallback() {
    this.classList.add("cursor-pointer");
    const id: string = `${SwitchHideResult.tag}-checkbox-${Date.now()}`;
    this.innerHTML = `
            <input type="checkbox" id="${id}" class="cursor-pointer" ${
      await store.getItem(SwitchHideResult.idLocalForage) ? "checked" : ""
    }/>
            <label for="${id}" class="cursor-pointer uppercase" ${ATTRIBUTE_TRANSLATION_KEY}="hideResults"></label>
        `;
    this.addEventListener("click", () => {
      const isChecked: boolean = this.active;
      store.setItem(SwitchHideResult.idLocalForage, isChecked);
      document.querySelectorAll<SwitchHideResult>(SwitchHideResult.tag).forEach(
        (switchHide: SwitchHideResult) => {
          if (switchHide !== this) {
            switchHide.active = isChecked;
          }
        },
      );
    });
  }
}
safeCustomDefine(SwitchHideResult);
