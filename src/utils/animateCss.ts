const prefix = "animate__";
/**
 * Animates the given element using animate.css. Returns a Promise that resolves
 * when the animation ends.
 *
 * @param element - The element to animate.
 * @param animation - The name of the animation to use.
 * @returns A Promise that resolves when the animation ends.
 */
export const animateCSS = (
  element: HTMLElement,
  animation: string,
  speed?: "slower" | "slow" | "fast" | "faster",
): Promise<boolean> => {
  // We create a Promise and return it
  return new Promise((resolve): void => {
    const animationName = `${prefix}${animation}`;
    const animations = [
      `${prefix}animated`,
      animationName,
      speed ? `animate__${speed}` : "",
    ].filter((a) => a);
    element.classList.add(...animations);
    // When the animation ends, we clean the classes and resolve the Promise
    function handleAnimationEnd(event: AnimationEvent): void {
      event.stopPropagation();
      element.classList.remove(...animations);
      resolve(true);
    }
    element.addEventListener("animationend", handleAnimationEnd, {
      once: true,
    });
  });
};
