export const checkMandatoryParameters: {
  mandatoryParameters: string[];
  checkMandatoryParameters(): void;
  [parameter: string]: unknown;
} = {
  mandatoryParameters: [] as string[],
  checkMandatoryParameters(): void {
    if (!this.mandatoryParameters) {
      console.error("mandatoryParameters getter is not defined");
    }
    this.mandatoryParameters?.forEach((parameter) => {
      if (!this[parameter]) {
        console.error(
          `Mandatory parameter ${parameter} is not set`,
          this.constructor.name,
        );
      }
    });
  },
};
// a function to assign the checkMandatoryParameters function to the prototype of the class
class DummyClass {}
export const assignCheckMandatoryParameters = (
  classToAssign: typeof DummyClass,
) => {
  Object.assign(classToAssign.prototype, checkMandatoryParameters);
};
