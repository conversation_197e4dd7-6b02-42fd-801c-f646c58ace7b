import { MessagesSection } from "components/sidebar/MessagesSection.ts";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "GLOBALS";
import { MessageDetails } from "types/communication.ts";
import { getDebugSetting } from "utils/get-debug-setting.ts";

type consoleActions = "log" | "warn" | "error" | "info" | "debug";
type logLocations = "console" | "messages" | "both";

export const devLog = async (
  message: unknown,
  options?: { action?: consoleActions; location?: logLocations },
) => {
  const logLocation = options?.location || "console";
  const isDebugOn = await getDebugSetting();
  if (!isDebugOn) {
    return;
  }
  if (logLocation === "console" || logLocation === "both") {
    console[options?.action || "log"](message);
  }
  if (logLocation === "messages" || logLocation === "both") {
    const messagesSection = document.querySelector(
      MessagesSection.tag,
    ) as MessagesSection;
    if (message !== null) {
      const messageToText = typeof message === "object"
        ? JSON.stringify(message, null, 0)
        : message?.toString();
      const messageDetails: MessageDetails = {
        type: "debug",
        from: `${GLOBALS.APP_NAME} debug`,
        text: messageToText,
      };
      await messagesSection?.add(messageDetails);
    }
  }
};
