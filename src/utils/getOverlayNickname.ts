// import { UserDetailsController } from "controllers/UserDetailsController.ts";

// export const getOverlayNickname = async (
//   nickname?: string,
// ): Promise<string> => {
//   const hostname: string = globalThis.location.hostname;
//   if (hostname.startsWith("meet.google.com")) {
//     if (nickname) return nickname;
//     const selfNameAttribute = `data-self-name`;
//     const selfName = document.querySelector(`[${selfNameAttribute}]`)
//       ?.getAttribute(selfNameAttribute);
//     return selfName || "";
//   }
//   if (hostname.startsWith("discord.com")) {
//     const userDetailsController = await UserDetailsController.instance;
//     const userNickname = nickname || await userDetailsController.nickname;
//     return userNickname;
//   }
//   return "";
// };
