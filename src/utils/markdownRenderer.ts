import { render } from "@deno/gfm";

/**
 * Renders a markdown file to HTML with proper styling
 * @param markdownContent The raw markdown content to render
 * @param language Optional language code for automatic translation
 * @returns A Response object with the rendered HTML
 */
export const renderMarkdown = async (
  markdownContent: string,
  language?: string,
): Promise<Response> => {
  // Render the markdown to HTML
  const renderedContent = render(markdownContent);

  // Extract the title from the markdown content (first h1)
  const titleRegex = /<h1[^>]*>([^<]+)<\/h1>/i;
  const titleMatch = renderedContent.match(titleRegex);
  const title = titleMatch ? titleMatch[1] : "Documentation";

  // Get the template HTML
  const templatePath =
    new URL("../templates/markdown-template.html", import.meta.url).pathname;
  let templateHtml = await Deno.readTextFile(templatePath);

  // Replace placeholders in the template
  templateHtml = templateHtml
    .replaceAll("{{title}}", title)
    .replaceAll("{{content}}", renderedContent)
    .replaceAll("{{language}}", language || "en");

  // Cache control constant
  const NO_CACHE = "no-store";

  // Apply no-cache headers
  const headers = new Headers();
  headers.set("Cache-Control", NO_CACHE);
  headers.set("Content-Type", "text/html");

  return new Response(templateHtml, {
    headers,
  });
};
