import { assertEquals, assertExists } from "@std/assert";
import {
  isVibrationSupported,
  vibrate,
  vibrateLightTap,
  vibrateMultipleDice,
  vibrateSingleDie,
  vibrateSpecialResult,
  VIBRATION_PATTERNS,
} from "./vibration.ts";

// Mock navigator.vibrate for testing
const originalNavigator = globalThis.navigator;
let vibrateCalls: (number | number[])[] = [];

const mockNavigator = {
  ...originalNavigator,
  vibrate: (pattern: number | number[]) => {
    vibrateCalls.push(pattern);
    return true;
  },
};

Deno.test("Vibration utility functions", async (t) => {
  // Setup mock
  Object.defineProperty(globalThis, "navigator", {
    value: mockNavigator,
    writable: true,
  });

  await t.step(
    "isVibrationSupported returns true when vibrate is available",
    () => {
      assertEquals(isVibrationSupported(), true);
    },
  );

  await t.step("vibrate function calls navigator.vibrate", () => {
    vibrateCalls = [];
    vibrate(100);
    assertEquals(vibrateCalls.length, 1);
    assertEquals(vibrateCalls[0], 100);
  });

  await t.step("vibrate function handles array patterns", () => {
    vibrateCalls = [];
    vibrate([100, 50, 100]);
    assertEquals(vibrateCalls.length, 1);
    assertEquals(vibrateCalls[0], [100, 50, 100]);
  });

  await t.step("vibrateSingleDie uses correct pattern", () => {
    vibrateCalls = [];
    vibrateSingleDie();
    assertEquals(vibrateCalls.length, 1);
    assertEquals(vibrateCalls[0], VIBRATION_PATTERNS.SINGLE_DIE);
  });

  await t.step("vibrateMultipleDice uses correct pattern", () => {
    vibrateCalls = [];
    vibrateMultipleDice();
    assertEquals(vibrateCalls.length, 1);
    assertEquals(
      vibrateCalls[0],
      VIBRATION_PATTERNS.MULTIPLE_DICE as unknown as number[],
    );
  });

  await t.step("vibrateSpecialResult uses correct pattern", () => {
    vibrateCalls = [];
    vibrateSpecialResult();
    assertEquals(vibrateCalls.length, 1);
    assertEquals(
      vibrateCalls[0],
      VIBRATION_PATTERNS.SPECIAL_RESULT as unknown as number[],
    );
  });

  await t.step("vibrateLightTap uses correct pattern", () => {
    vibrateCalls = [];
    vibrateLightTap();
    assertEquals(vibrateCalls.length, 1);
    assertEquals(vibrateCalls[0], VIBRATION_PATTERNS.LIGHT_TAP);
  });

  await t.step("vibration patterns are defined correctly", () => {
    assertExists(VIBRATION_PATTERNS.SINGLE_DIE);
    assertExists(VIBRATION_PATTERNS.MULTIPLE_DICE);
    assertExists(VIBRATION_PATTERNS.SPECIAL_RESULT);
    assertExists(VIBRATION_PATTERNS.LIGHT_TAP);

    assertEquals(typeof VIBRATION_PATTERNS.SINGLE_DIE, "number");
    assertEquals(Array.isArray(VIBRATION_PATTERNS.MULTIPLE_DICE), true);
    assertEquals(Array.isArray(VIBRATION_PATTERNS.SPECIAL_RESULT), true);
    assertEquals(typeof VIBRATION_PATTERNS.LIGHT_TAP, "number");
  });

  // Cleanup
  Object.defineProperty(globalThis, "navigator", {
    value: originalNavigator,
    writable: true,
  });
});

Deno.test("Vibration without support", async (t) => {
  // Setup mock without vibrate support
  const mockNavigatorNoVibrate = {
    ...originalNavigator,
  };
  delete (mockNavigatorNoVibrate as Record<string, unknown>).vibrate;

  Object.defineProperty(globalThis, "navigator", {
    value: mockNavigatorNoVibrate,
    writable: true,
  });

  await t.step(
    "isVibrationSupported returns false when vibrate is not available",
    () => {
      assertEquals(isVibrationSupported(), false);
    },
  );

  await t.step("vibrate function does nothing when not supported", () => {
    // Should not throw an error
    vibrate(100);
    vibrateSingleDie();
    vibrateMultipleDice();
  });

  // Cleanup
  Object.defineProperty(globalThis, "navigator", {
    value: originalNavigator,
    writable: true,
  });
});
