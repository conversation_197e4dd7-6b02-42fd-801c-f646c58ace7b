/**
 * Vibration utility functions for haptic feedback on mobile devices
 */

/**
 * Check if the Vibration API is supported by the current browser/device
 */
export const isVibrationSupported = (): boolean => {
  return "vibrate" in navigator;
};

/**
 * Trigger vibration with a specific pattern
 * @param pattern - Vibration pattern (number for duration or array for pattern)
 */
export const vibrate = (pattern: number | readonly number[]): void => {
  if (!isVibrationSupported()) {
    return;
  }

  try {
    navigator.vibrate(pattern as number | number[]);
  } catch (error) {
    // Silently fail if vibration is not available or fails
    console.debug("Vibration failed:", error);
  }
};

/**
 * Predefined vibration patterns for different dice actions
 */
export const VIBRATION_PATTERNS = {
  // Single die throw - short, sharp vibration
  SINGLE_DIE: 100,

  // Multiple dice throw - longer vibration with pattern
  MULTIPLE_DICE: [150, 50, 100],

  // Critical hit or special result - distinctive pattern
  SPECIAL_RESULT: [200, 100, 200, 100, 200],

  // Light tap for UI interactions
  LIGHT_TAP: 50,
} as const;

/**
 * Vibrate for a single die throw
 */
export const vibrateSingleDie = (): void => {
  vibrate(VIBRATION_PATTERNS.SINGLE_DIE);
};

/**
 * Vibrate for multiple dice throw
 */
export const vibrateMultipleDice = (): void => {
  vibrate(VIBRATION_PATTERNS.MULTIPLE_DICE);
};

/**
 * Vibrate for special results (like critical hits)
 */
export const vibrateSpecialResult = (): void => {
  vibrate(VIBRATION_PATTERNS.SPECIAL_RESULT);
};

/**
 * Light vibration for general UI interactions
 */
export const vibrateLightTap = (): void => {
  vibrate(VIBRATION_PATTERNS.LIGHT_TAP);
};
